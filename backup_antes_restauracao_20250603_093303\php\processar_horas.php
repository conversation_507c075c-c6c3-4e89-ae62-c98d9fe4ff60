<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header("Location: Projeto.php?msg=" . urlencode("Você não tem permissão para acessar esta página"));
    exit();
}

// Verificar token CSRF
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    $_SESSION['mensagem'] = "Erro de segurança. Por favor, tente novamente.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Verificar se é uma edição ou um novo registro
$acao = isset($_POST['acao']) ? $_POST['acao'] : 'novo';

if ($acao === 'editar') {
    // Verificar se o usuário tem permissão para editar horas (apenas cargos 1 e 2)
    if (!isset($_SESSION['cargo_utilizador']) || $_SESSION['cargo_utilizador'] == '3' || $_SESSION['cargo_utilizador'] == '4') {
        $_SESSION['mensagem'] = "Você não tem permissão para editar registros de horas";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 4.php");
        exit();
    }

    // Editar registro existente
    if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
        $_SESSION['mensagem'] = "ID inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 4.php");
        exit();
    }

    $id = intval($_POST['id']);
    $obra_id = intval($_POST['obra_id']);
    $funcionario_id = isset($_POST['funcionario_id']) ? intval($_POST['funcionario_id']) :
                      (isset($_SESSION['user_id']) ? $_SESSION['user_id'] :
                      (isset($_SESSION['id_utilizadores']) ? $_SESSION['id_utilizadores'] : 1));
    $data = mysqli_real_escape_string($conn, $_POST['data']);
    $horas = floatval($_POST['horas']);
    $descricao = mysqli_real_escape_string($conn, $_POST['descricao']);

    // Verificar a estrutura da tabela para identificar o nome correto da coluna ID
    $result_structure = mysqli_query($conn, "DESCRIBE registro_horas");
    $id_column_name = null;

    if ($result_structure) {
        while ($column = mysqli_fetch_assoc($result_structure)) {
            if ($column['Key'] == 'PRI') {
                $id_column_name = $column['Field'];
                break;
            }
        }
    }

    // Se não encontramos a chave primária, vamos tentar alguns nomes comuns
    if (!$id_column_name) {
        $possible_id_names = ['id', 'registro_id', 'id_registro', 'hora_id', 'id_hora'];
        $table_columns = [];

        $result_columns = mysqli_query($conn, "SHOW COLUMNS FROM registro_horas");
        while ($column = mysqli_fetch_assoc($result_columns)) {
            $table_columns[] = $column['Field'];
        }

        foreach ($possible_id_names as $possible_name) {
            if (in_array($possible_name, $table_columns)) {
                $id_column_name = $possible_name;
                break;
            }
        }
    }

    // Se ainda não encontramos, vamos usar um valor padrão
    if (!$id_column_name) {
        $id_column_name = 'id';
    }

    // Atualizar o registro
    $query = "UPDATE registro_horas SET
              id_obra = ?,
              id_usuario = ?,
              data_registro = ?,
              horas = ?,
              descricao = ?
              WHERE $id_column_name = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "iisdsi", $obra_id, $funcionario_id, $data, $horas, $descricao, $id);

    if (mysqli_stmt_execute($stmt)) {
        $_SESSION['mensagem'] = "Registro atualizado com sucesso!";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao atualizar registro: " . mysqli_error($conn);
        $_SESSION['tipo_mensagem'] = "danger";
    }

    mysqli_stmt_close($stmt);
} else {
    // Novo registro
    $obra_id = intval($_POST['obra_id']);
    $funcionario_id = isset($_POST['funcionario_id']) ? intval($_POST['funcionario_id']) :
                      (isset($_SESSION['user_id']) ? $_SESSION['user_id'] :
                      (isset($_SESSION['id_utilizadores']) ? $_SESSION['id_utilizadores'] : 1));
    $data = mysqli_real_escape_string($conn, $_POST['data']);
    $horas = floatval($_POST['horas']);
    $descricao = mysqli_real_escape_string($conn, $_POST['descricao']);

    // Inserir o novo registro
    $query = "INSERT INTO registro_horas (id_obra, id_usuario, data_registro, horas, descricao)
              VALUES (?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "iisds", $obra_id, $funcionario_id, $data, $horas, $descricao);

    if (mysqli_stmt_execute($stmt)) {
        $_SESSION['mensagem'] = "Registro criado com sucesso!";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao criar registro: " . mysqli_error($conn);
        $_SESSION['tipo_mensagem'] = "danger";
    }

    mysqli_stmt_close($stmt);
}

mysqli_close($conn);
header("Location: Projeto pag 4.php");
exit();
?>