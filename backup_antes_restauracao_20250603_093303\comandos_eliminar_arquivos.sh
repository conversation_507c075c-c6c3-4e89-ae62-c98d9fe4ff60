#!/bin/bash

echo "=== SCRIPT DE LIMPEZA SEGURA DE FICHEIROS DESNECESSÁRIOS ==="
echo "Este script irá eliminar apenas ficheiros seguros, mantendo todas as funcionalidades."
echo ""

# Contador de ficheiros eliminados
count=0

# Função para eliminar ficheiro se existir
safe_remove() {
    if [ -f "$1" ]; then
        rm -f "$1"
        echo "✓ Eliminado: $1"
        ((count++))
    else
        echo "- Não encontrado: $1"
    fi
}

# Função para eliminar diretório se existir
safe_remove_dir() {
    if [ -d "$1" ]; then
        rm -rf "$1"
        echo "✓ Diretório eliminado: $1"
        ((count++))
    else
        echo "- Diretório não encontrado: $1"
    fi
}

echo "1. ELIMINANDO FICHEIROS DE TESTE E DEBUG..."
echo "============================================="

# Ficheiros de teste na raiz
safe_remove "test_app_functions.php"
safe_remove "output.html"

# Ficheiros de teste e debug no diretório php
safe_remove "PHP PROJETO/php/test_admin.php"
safe_remove "PHP PROJETO/php/test_admin_login.php"
safe_remove "PHP PROJETO/php/test_admin_menu.php"
safe_remove "PHP PROJETO/php/test_database.php"
safe_remove "PHP PROJETO/php/test_menu.php"
safe_remove "PHP PROJETO/php/test_security.php"

# Ficheiros de debug
safe_remove "PHP PROJETO/php/debug_admin.php"
safe_remove "PHP PROJETO/php/debug_db.php"
safe_remove "PHP PROJETO/php/debug_form.js"
safe_remove "PHP PROJETO/php/debug_menu.js"
safe_remove "PHP PROJETO/php/debug_session.php"
safe_remove "PHP PROJETO/php/debug_csrf.js"

# Ficheiros de verificação e diagnóstico
safe_remove "PHP PROJETO/php/check_admin.php"
safe_remove "PHP PROJETO/php/check_file.php"
safe_remove "PHP PROJETO/php/check_session.php"
safe_remove "PHP PROJETO/php/diagnostico.php"

echo ""
echo "2. ELIMINANDO FICHEIROS DE ADMINISTRAÇÃO TEMPORÁRIOS..."
echo "======================================================="

safe_remove "PHP PROJETO/php/force_admin.php"
safe_remove "PHP PROJETO/php/direct_admin_access.php"
safe_remove "PHP PROJETO/php/set_admin.php"
safe_remove "PHP PROJETO/php/go_to_admin.php"
safe_remove "PHP PROJETO/php/admin_acesso.php"
safe_remove "PHP PROJETO/php/admin_button.php"
safe_remove "PHP PROJETO/php/admin_page_copy.php"

echo ""
echo "3. ELIMINANDO FICHEIROS DE CONFIGURAÇÃO TEMPORÁRIOS..."
echo "======================================================"

safe_remove "PHP PROJETO/php/corrigir_tabela_obras.php"
safe_remove "PHP PROJETO/php/copiar_imagens_manual.php"
safe_remove "PHP PROJETO/php/atualizar_proximos_prazos.php"
safe_remove "PHP PROJETO/php/clear_session.php"

echo ""
echo "4. ELIMINANDO FICHEIROS JAVASCRIPT DE DEBUG..."
echo "=============================================="

# MANTER: fix_admin_menu.js, csrf_fix.js, csrf_init.js (são utilizados)
# ELIMINAR: apenas os de debug
safe_remove "PHP PROJETO/php/admin_menu_debug.js"
safe_remove "PHP PROJETO/php/debug_menu.js"
safe_remove "PHP PROJETO/php/menu_fix.js"

echo ""
echo "5. ELIMINANDO SCRIPTS DE REVERSÃO..."
echo "===================================="

safe_remove "php/revert_paths.php"

echo ""
echo "6. ELIMINANDO PROJETO HOTEL COMPLETO..."
echo "======================================="

safe_remove_dir "Hotel_Estrela_Mar_(php)"

echo ""
echo "7. ELIMINANDO FICHEIROS HTML ESTÁTICOS..."
echo "========================================="

safe_remove_dir "PHP PROJETO/HTML"

echo ""
echo "8. ELIMINANDO FICHEIROS DE EXEMPLO..."
echo "====================================="

safe_remove_dir "PHP action"

echo ""
echo "9. ELIMINANDO DOCUMENTAÇÃO E IMAGENS AUXILIARES..."
echo "=================================================="

safe_remove_dir "Word"
safe_remove_dir "PHP PROJETO/imagens_aux"
safe_remove "Gestao-Digital-de-Obras-Projeto-de-Aplicacao-Web.pptx.pdf.pptx"
safe_remove "Planeador de projetos Gantt1 (version 1).xlsb.xlsx"

echo ""
echo "10. ELIMINANDO LOGS E FICHEIROS TEMPORÁRIOS..."
echo "=============================================="

safe_remove_dir "PHP PROJETO/php/logs"

echo ""
echo "=== RESUMO DA LIMPEZA ==="
echo "Ficheiros/diretórios eliminados: $count"
echo ""
echo "✅ FICHEIROS MANTIDOS (ESSENCIAIS):"
echo "- Todas as páginas principais (Projeto*.php)"
echo "- Ficheiros de processamento (processar_*.php)"
echo "- Ficheiros de segurança (conexao.php, verificar_permissao.php, etc.)"
echo "- Ficheiros CSS essenciais"
echo "- Ficheiros JavaScript funcionais (fix_admin_menu.js, csrf_*.js)"
echo "- Imagens utilizadas (Imagem1.png)"
echo ""
echo "🗑️ FICHEIROS ELIMINADOS:"
echo "- Ficheiros de teste e debug"
echo "- Projeto Hotel completo"
echo "- Ficheiros HTML estáticos"
echo "- Documentação e imagens auxiliares"
echo "- Scripts temporários e de reversão"
echo ""
echo "✅ Limpeza concluída com sucesso!"
echo "✅ Todas as funcionalidades principais foram preservadas."
