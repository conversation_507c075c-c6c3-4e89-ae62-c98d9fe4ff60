<?php
session_start();

// Incluir arquivos necessários
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário tem permissão (apenas nível 1 - administrador)
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')) {
    $_SESSION['AccessError'] = "Acesso negado. Apenas administradores podem acessar esta página.";
    header("Location: Projeto.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administração - Built Organizer</title>
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu" class="active">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content" style="padding-top: 80px;">
            <div class="container">
                <!-- Mensagens de alerta -->
                <?php if(isset($_SESSION['mensagem'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['mensagem']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    </div>
                    <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
                <?php endif; ?>

                <?php if(isset($_GET['msg'])): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($_GET['msg']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    </div>
                <?php endif; ?>

                <!-- Botões de navegação do painel de administração -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-center">
                            <a href="listar_utilizadores.php" class="btn btn-secondary mx-2">
                                <i class="bi bi-people"></i> Lista de Utilizadores
                            </a>
                        </div>
                    </div>
                </div>

                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h2>Painel de Administração</h2>
                                <p class="mb-0">Registar Novo Utilizador</p>
                            </div>
                            <div class="card-body">

                                <form action="Criar_utilizador.php" method="POST">
                                    <!-- Token CSRF para segurança -->
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <div class="mb-3">
                                        <label for="nome_utilizador" class="form-label">Nome de Utilizador</label>
                                        <input type="text" class="form-control" id="nome_utilizador" name="nome_utilizador" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email_utilizador" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email_utilizador" name="email_utilizador" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password_utilizador" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password_utilizador" name="password_utilizador" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password_utilizador_confirma" class="form-label">Confirmar Password</label>
                                        <input type="password" class="form-control" id="password_utilizador_confirma" name="password_utilizador_confirma" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="cargo_utilizador" class="form-label">Nível de Acesso</label>
                                        <select class="form-select" id="cargo_utilizador" name="cargo_utilizador" required>
                                            <option value="">Selecione o nível</option>
                                            <?php if($_SESSION['cargo_utilizador'] == '1'): ?>
                                                <option value="1">Nível 1 (Administrador)</option>
                                            <?php endif; ?>
                                            <option value="2">Nível 2 (Gestor)</option>
                                            <option value="3">Nível 3 (Supervisor)</option>
                                            <option value="4">Nível 4 (Trabalhador)</option>
                                        </select>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-person-plus"></i> Registar Utilizador
                                        </button>
                                        <a href="Projeto.php" class="btn btn-secondary">
                                            <i class="bi bi-x-circle"></i> Cancelar
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <div class="container">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> Built Organizer. Todos os direitos reservados.</p>
            </div>
        </footer>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>
</body>
</html>


