<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Sem permissão para acessar este recurso']);
    exit;
}

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'ID inválido']);
    exit;
}

$id = intval($_GET['id']);
$conn = connectToDatabase();

// Buscar o registro pelo ID
$query = "SELECT * FROM registro_horas WHERE id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "i", $id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($row = mysqli_fetch_assoc($result)) {
    // Retornar os dados do registro em formato JSON
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'id' => $row['id'],
        'id_obra' => $row['id_obra'],
        'id_usuario' => $row['id_usuario'],
        'data_registro' => $row['data_registro'],
        'horas' => $row['horas'],
        'descricao' => $row['descricao']
    ]);
} else {
    // Registro não encontrado
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Registro não encontrado']);
}

mysqli_stmt_close($stmt);
mysqli_close($conn);
?>
