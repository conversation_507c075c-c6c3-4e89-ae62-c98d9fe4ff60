<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header("Location: Projeto.php?msg=Você não tem permissão para acessar esta página");
    exit;
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Função para validar dados
function validarDados($obra, $data, $horas, $descricao) {
    $erros = [];
    
    if (empty($obra)) {
        $erros[] = "Selecione uma obra";
    }
    
    if (empty($data)) {
        $erros[] = "Informe a data";
    } else {
        // Verificar se a data é válida
        $data_atual = date('Y-m-d');
        if ($data > $data_atual) {
            $erros[] = "A data não pode ser futura";
        }
    }
    
    if (empty($horas)) {
        $erros[] = "Informe as horas trabalhadas";
    } else if ($horas <= 0 || $horas > 24) {
        $erros[] = "As horas devem estar entre 0.5 e 24";
    }
    
    if (empty($descricao)) {
        $erros[] = "Informe uma descrição";
    }
    
    return $erros;
}

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['acao'])) {
    $acao = isset($_POST['acao']) ? $_POST['acao'] : $_GET['acao'];
    
    // Adicionar novo registro
    if ($acao === 'adicionar' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $obra = isset($_POST['obra']) ? $_POST['obra'] : '';
        $data = isset($_POST['data']) ? $_POST['data'] : '';
        $horas = isset($_POST['horas']) ? $_POST['horas'] : '';
        $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : '';
        
        // Validar dados
        $erros = validarDados($obra, $data, $horas, $descricao);
        
        if (empty($erros)) {
            // Obter ID do usuário logado
            $nome_usuario = $_SESSION['userName'];
            $query_usuario = "SELECT id_utilizadores FROM utilizadores WHERE nome_utilizador = ?";
            $stmt = mysqli_prepare($conn, $query_usuario);
            mysqli_stmt_bind_param($stmt, "s", $nome_usuario);
            mysqli_stmt_execute($stmt);
            $result_usuario = mysqli_stmt_get_result($stmt);
            
            if ($row_usuario = mysqli_fetch_assoc($result_usuario)) {
                $id_usuario = $row_usuario['id_utilizadores'];
                
                // Inserir registro
                $query_inserir = "INSERT INTO registro_horas (id_obra, id_usuario, horas, data_registro, descricao) 
                                 VALUES (?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $query_inserir);
                mysqli_stmt_bind_param($stmt, "iidss", $obra, $id_usuario, $horas, $data, $descricao);
                
                if (mysqli_stmt_execute($stmt)) {
                    $_SESSION['mensagem'] = "Registro de horas adicionado com sucesso!";
                    $_SESSION['tipo_mensagem'] = "success";
                } else {
                    $_SESSION['mensagem'] = "Erro ao adicionar registro: " . mysqli_error($conn);
                    $_SESSION['tipo_mensagem'] = "danger";
                }
            } else {
                $_SESSION['mensagem'] = "Erro ao identificar usuário.";
                $_SESSION['tipo_mensagem'] = "danger";
            }
        } else {
            $_SESSION['mensagem'] = "Erro: " . implode(", ", $erros);
            $_SESSION['tipo_mensagem'] = "danger";
        }
        
        header("Location: Projeto pag 4.php");
        exit;
    }
    
    // Editar registro existente
    else if ($acao === 'editar' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $id_registro = isset($_POST['id_registro']) ? $_POST['id_registro'] : '';
        $obra = isset($_POST['obra']) ? $_POST['obra'] : '';
        $data = isset($_POST['data']) ? $_POST['data'] : '';
        $horas = isset($_POST['horas']) ? $_POST['horas'] : '';
        $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : '';
        
        // Validar dados
        $erros = validarDados($obra, $data, $horas, $descricao);
        
        if (empty($erros)) {
            // Atualizar registro
            $query_atualizar = "UPDATE registro_horas 
                              SET id_obra = ?, horas = ?, data_registro = ?, descricao = ? 
                              WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query_atualizar);
            mysqli_stmt_bind_param($stmt, "idssi", $obra, $horas, $data, $descricao, $id_registro);
            
            if (mysqli_stmt_execute($stmt)) {
                $_SESSION['mensagem'] = "Registro atualizado com sucesso!";
                $_SESSION['tipo_mensagem'] = "success";
            } else {
                $_SESSION['mensagem'] = "Erro ao atualizar registro: " . mysqli_error($conn);
                $_SESSION['tipo_mensagem'] = "danger";
            }
        } else {
            $_SESSION['mensagem'] = "Erro: " . implode(", ", $erros);
            $_SESSION['tipo_mensagem'] = "danger";
        }
        
        header("Location: Projeto pag 4.php");
        exit;
    }
    
    // Excluir registro
    else if ($acao === 'excluir' && isset($_GET['id'])) {
        $id_registro = $_GET['id'];
        
        // Excluir registro
        $query_excluir = "DELETE FROM registro_horas WHERE id = ?";
        $stmt = mysqli_prepare($conn, $query_excluir);
        mysqli_stmt_bind_param($stmt, "i", $id_registro);
        
        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['mensagem'] = "Registro excluído com sucesso!";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao excluir registro: " . mysqli_error($conn);
            $_SESSION['tipo_mensagem'] = "danger";
        }
        
        header("Location: Projeto pag 4.php");
        exit;
    }
}

// Redirecionar para a página principal se nenhuma ação for especificada
header("Location: Projeto pag 4.php");
exit;
?>




