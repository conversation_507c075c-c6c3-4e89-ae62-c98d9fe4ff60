-- Script para inserir dados de teste na aplicação Built Organizer
-- Execute este script após criar o banco de dados

USE controle_obras;

-- Inserir cargos se não existirem
INSERT INTO cargos (id, cargo, nivel_acesso) VALUES 
(1, 'Administrador', 1),
(2, '<PERSON><PERSON><PERSON>', 2),
(3, 'Supervisor', 3),
(4, 'Funcionário', 4)
ON DUPLICATE KEY UPDATE cargo = VALUES(cargo), nivel_acesso = VALUES(nivel_acesso);

-- Inserir usuários de teste
-- Senha para todos: 123456
INSERT INTO utilizadores (nome_utilizador, email_utilizador, password_utilizador, cargo_utilizador) VALUES 
('admin', '<EMAIL>', '123456', 1),
('gerente', '<EMAIL>', '123456', 2),
('supervisor', '<EMAIL>', '123456', 3),
('funcionario', '<EMAIL>', '123456', 4)
ON DUPLICATE KEY UPDATE 
email_utilizador = VALUES(email_utilizador),
password_utilizador = VALUES(password_utilizador),
cargo_utilizador = VALUES(cargo_utilizador);

-- Inserir obras de exemplo
INSERT INTO obras (nome_obra, endereço, data_inicio, data_fim) VALUES 
('Construção de Prédio Residencial', 'Rua das Flores, 123, Lisboa', '2024-01-15', '2024-12-30'),
('Reforma de Escritório Comercial', 'Avenida Central, 456, Porto', '2024-03-10', '2024-08-15'),
('Construção de Moradia Unifamiliar', 'Rua do Sol, 789, Faro', '2024-05-20', '2024-11-28'),
('Ampliação de Armazém Industrial', 'Zona Industrial, Lote 10, Braga', '2024-02-01', '2024-09-30')
ON DUPLICATE KEY UPDATE 
endereço = VALUES(endereço),
data_inicio = VALUES(data_inicio),
data_fim = VALUES(data_fim);

-- Inserir materiais de exemplo
INSERT INTO materiais (nome, quantidade) VALUES 
('Cimento Portland', 500),
('Tijolo Cerâmico', 10000),
('Ferro para Construção', 2000),
('Areia Fina', 100),
('Brita', 150),
('Tinta Acrílica Branca', 50),
('Azulejo Branco 30x30', 1000),
('Piso Cerâmico', 500)
ON DUPLICATE KEY UPDATE quantidade = VALUES(quantidade);

-- Criar tabela de orçamentos se não existir
CREATE TABLE IF NOT EXISTS orcamentos (
    id INT NOT NULL AUTO_INCREMENT,
    id_obra INT NOT NULL,
    descricao TEXT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    data_criacao DATE NOT NULL,
    status VARCHAR(50) DEFAULT 'Pendente',
    PRIMARY KEY (id),
    FOREIGN KEY (id_obra) REFERENCES obras(obras_id) ON DELETE CASCADE
);

-- Inserir orçamentos de exemplo
INSERT INTO orcamentos (id_obra, descricao, valor, data_criacao, status) VALUES 
(1, 'Orçamento para fundação e estrutura', 450000.00, '2024-01-10', 'Aprovado'),
(1, 'Orçamento para acabamentos', 280000.00, '2024-06-15', 'Pendente'),
(2, 'Orçamento para demolição e reforma', 120000.00, '2024-03-05', 'Aprovado'),
(3, 'Orçamento completo da moradia', 350000.00, '2024-05-15', 'Em análise'),
(4, 'Orçamento para ampliação', 180000.00, '2024-01-28', 'Aprovado')
ON DUPLICATE KEY UPDATE 
descricao = VALUES(descricao),
valor = VALUES(valor),
status = VALUES(status);

-- Criar tabela de registro de horas se não existir
CREATE TABLE IF NOT EXISTS registro_horas (
    id INT NOT NULL AUTO_INCREMENT,
    id_obra INT NOT NULL,
    id_usuario INT NOT NULL,
    horas DECIMAL(5,2) NOT NULL,
    data_registro DATE NOT NULL,
    descricao TEXT NULL,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (id_obra) REFERENCES obras(obras_id) ON DELETE CASCADE,
    FOREIGN KEY (id_usuario) REFERENCES utilizadores(id_utilizadores) ON DELETE CASCADE
);

-- Inserir registros de horas de exemplo
INSERT INTO registro_horas (id_obra, id_usuario, horas, data_registro, descricao) VALUES 
(1, 3, 8.0, '2024-01-16', 'Trabalho na fundação - escavação'),
(1, 4, 8.0, '2024-01-16', 'Trabalho na fundação - armação'),
(1, 3, 7.5, '2024-01-17', 'Concretagem da fundação'),
(1, 4, 7.5, '2024-01-17', 'Concretagem da fundação'),
(2, 3, 6.0, '2024-03-11', 'Demolição de paredes internas'),
(2, 4, 6.0, '2024-03-11', 'Remoção de entulho'),
(3, 4, 8.0, '2024-05-21', 'Início da construção - marcação do terreno'),
(4, 3, 7.0, '2024-02-02', 'Preparação do terreno para ampliação')
ON DUPLICATE KEY UPDATE 
horas = VALUES(horas),
descricao = VALUES(descricao);

-- Exibir resumo dos dados inseridos
SELECT 'Dados de teste inseridos com sucesso!' as Status;
SELECT COUNT(*) as Total_Usuarios FROM utilizadores;
SELECT COUNT(*) as Total_Obras FROM obras;
SELECT COUNT(*) as Total_Materiais FROM materiais;
SELECT COUNT(*) as Total_Orcamentos FROM orcamentos;
SELECT COUNT(*) as Total_Registros_Horas FROM registro_horas;
