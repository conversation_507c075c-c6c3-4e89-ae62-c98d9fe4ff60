-- <PERSON><PERSON><PERSON> Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- -----------------------------------------------------
-- Schema mydb
-- -----------------------------------------------------
-- -----------------------------------------------------
-- Schema controle_obras
-- -----------------------------------------------------
DROP SCHEMA IF EXISTS `controle_obras` ;

-- -----------------------------------------------------
-- Schema controle_obras
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `controle_obras` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ;
USE `controle_obras` ;

-- -----------------------------------------------------
-- Table `controle_obras`.`cargos`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`cargos` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`cargos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `cargo` VARCHAR(100) NOT NULL,
  `nivel_acesso` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;


-- -----------------------------------------------------
-- Table `controle_obras`.`materiais`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`materiais` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`materiais` (
  `material_id` INT NOT NULL AUTO_INCREMENT,
  `nome` VARCHAR(255) NOT NULL,
  `quantidade` INT NOT NULL,
  PRIMARY KEY (`material_id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;


-- -----------------------------------------------------
-- Table `controle_obras`.`obras`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`obras` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`obras` (
  `obras_id` INT NOT NULL AUTO_INCREMENT,
  `nome_obra` VARCHAR(255) NOT NULL,
  `endereço` TEXT NOT NULL,
  `data_inicio` DATE NOT NULL,
  `data_fim` DATE NOT NULL,
  PRIMARY KEY (`obras_id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;


-- -----------------------------------------------------
-- Table `controle_obras`.`servicos_obra`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`servicos_obra` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`servicos_obra` (
  `id_servicos_obra` INT NOT NULL AUTO_INCREMENT,
  `id_obra` INT NULL DEFAULT NULL,
  `qt_materiais` INT NULL DEFAULT NULL,
  `data` DATE NOT NULL,
  `total_hrs` INT NOT NULL,
  `servicos_obracol` VARCHAR(45) NULL,
  PRIMARY KEY (`id_servicos_obra`),
  CONSTRAINT `servicos_obra`
    FOREIGN KEY (`id_obra`)
    REFERENCES `controle_obras`.`obras` (`obras_id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `obra_servicos_idx` ON `controle_obras`.`servicos_obra` (`id_obra` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `controle_obras`.`utilizadores`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`utilizadores` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`utilizadores` (
  `id_utilizadores` INT NOT NULL AUTO_INCREMENT,
  `nome_utilizador` VARCHAR(45) NOT NULL UNIQUE,
  `email_utilizador` VARCHAR(45) NOT NULL,
  `password_utilizador` VARCHAR(45) NOT NULL,
  `cargo_utilizador` INT NULL DEFAULT NULL,
  PRIMARY KEY (`id_utilizadores`),
  CONSTRAINT `utilizadores_cargos`
    FOREIGN KEY (`cargo_utilizador`)
    REFERENCES `controle_obras`.`cargos` (`id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `utilizadores_cargos_idx` ON `controle_obras`.`utilizadores` (`cargo_utilizador` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `controle_obras`.`obras_has_materiais`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`obras_has_materiais` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`obras_has_materiais` (
  `obras_obras_id` INT NOT NULL,
  `materiais_material_id` INT NOT NULL,
  PRIMARY KEY (`obras_obras_id`, `materiais_material_id`),
  CONSTRAINT `fk_obras_has_materiais_obras1`
    FOREIGN KEY (`obras_obras_id`)
    REFERENCES `controle_obras`.`obras` (`obras_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_obras_has_materiais_materiais1`
    FOREIGN KEY (`materiais_material_id`)
    REFERENCES `controle_obras`.`materiais` (`material_id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `fk_obras_has_materiais_materiais1_idx` ON `controle_obras`.`obras_has_materiais` (`materiais_material_id` ASC) VISIBLE;

CREATE INDEX `fk_obras_has_materiais_obras1_idx` ON `controle_obras`.`obras_has_materiais` (`obras_obras_id` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `controle_obras`.`servicos_obra_has_utilizadores`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`servicos_obra_has_utilizadores` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`servicos_obra_has_utilizadores` (
  `servicos_obra_id_servicos_obra` INT NOT NULL,
  `utilizadores_id_utilizadores` INT NOT NULL,
  PRIMARY KEY (`servicos_obra_id_servicos_obra`, `utilizadores_id_utilizadores`),
  CONSTRAINT `fk_servicos_obra_has_utilizadores_servicos_obra1`
    FOREIGN KEY (`servicos_obra_id_servicos_obra`)
    REFERENCES `controle_obras`.`servicos_obra` (`id_servicos_obra`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_servicos_obra_has_utilizadores_utilizadores1`
    FOREIGN KEY (`utilizadores_id_utilizadores`)
    REFERENCES `controle_obras`.`utilizadores` (`id_utilizadores`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `fk_servicos_obra_has_utilizadores_utilizadores1_idx` ON `controle_obras`.`servicos_obra_has_utilizadores` (`utilizadores_id_utilizadores` ASC) VISIBLE;

CREATE INDEX `fk_servicos_obra_has_utilizadores_servicos_obra1_idx` ON `controle_obras`.`servicos_obra_has_utilizadores` (`servicos_obra_id_servicos_obra` ASC) VISIBLE;


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
