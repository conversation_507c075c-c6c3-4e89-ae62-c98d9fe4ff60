// Script para garantir que o menu ADMINISTRAÇÃO seja exibido corretamente
document.addEventListener('DOMContentLoaded', function() {
    console.log("Script fix_admin_menu.js carregado!");

    // Encontrar todos os links para a página de administração (por href ou por id)
    var adminLinks = document.querySelectorAll('a[href="Registar_utilizador.php"], a#admin-menu');

    if (adminLinks.length > 0) {
        console.log("Menu de administração encontrado! Total: " + adminLinks.length);

        // Aplicar o estilo correto a todos os links de administração encontrados
        adminLinks.forEach(function(link) {
            console.log("Processando link: " + link.textContent + " (href: " + link.getAttribute('href') + ")");

            // Remover qualquer estilo inline que possa estar aplicando estilos indesejados
            link.style.backgroundColor = '';
            link.style.color = '';
            link.style.padding = '';
            link.style.borderRadius = '';
            link.style.fontWeight = '';
            link.style.display = 'block';

            // Garantir que o elemento pai (li) seja exibido
            if (link.parentElement && link.parentElement.tagName === 'LI') {
                console.log("Corrigindo estilo do elemento pai (LI)");
                link.parentElement.style.display = 'inline-block !important';
                link.parentElement.style.visibility = 'visible !important';
                link.parentElement.style.opacity = '1 !important';

                // Adicionar uma classe para garantir a visibilidade
                link.parentElement.classList.add('visible-menu-item');
            }

            // Garantir que o link seja visível
            link.style.visibility = 'visible';
            link.style.opacity = '1';

            console.log("Estilo do menu de administração corrigido!");
        });
    } else {
        console.log("Menu de administração não encontrado!");

        // Tentar encontrar o menu por texto
        var allLinks = document.querySelectorAll('.Menu a, nav a');
        for (var i = 0; i < allLinks.length; i++) {
            if (allLinks[i].textContent.trim() === 'ADMINISTRAÇÃO') {
                console.log("Menu de administração encontrado pelo texto!");
                var adminLink = allLinks[i];

                // Aplicar os mesmos estilos
                adminLink.style.display = 'block';
                adminLink.style.visibility = 'visible';
                adminLink.style.opacity = '1';

                if (adminLink.parentElement && adminLink.parentElement.tagName === 'LI') {
                    adminLink.parentElement.style.display = 'inline-block !important';
                    adminLink.parentElement.style.visibility = 'visible !important';
                    adminLink.parentElement.style.opacity = '1 !important';
                    adminLink.parentElement.classList.add('visible-menu-item');
                }

                break;
            }
        }
    }
});
