<?php
require_once 'conexao.php';

// Ativar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Listagem de Obras no Banco de Dados</h1>";

// Estabelecer conexão com o banco de dados
$conn = connectToDatabase();

// Verificar conexão
if (!$conn) {
    die("<p style='color:red'>Erro de conexão: " . mysqli_connect_error() . "</p>");
}

echo "<p style='color:green'>Conexão com o banco de dados estabelecida com sucesso.</p>";

// Verificar se a tabela obras existe
$query = "SHOW TABLES LIKE 'obras'";
$result = mysqli_query($conn, $query);
$table_exists = mysqli_num_rows($result) > 0;

if ($table_exists) {
    echo "<p style='color:green'>A tabela 'obras' existe.</p>";
    
    // Verificar a estrutura da tabela
    $query = "DESCRIBE obras";
    $result = mysqli_query($conn, $query);
    $colunas = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $colunas[] = $row['Field'];
    }
    
    echo "<h2>Colunas da tabela 'obras':</h2>";
    echo "<ul>";
    foreach ($colunas as $coluna) {
        echo "<li>$coluna</li>";
    }
    echo "</ul>";
    
    // Determinar o nome correto das colunas
    $coluna_id = in_array('obras_id', $colunas) ? 'obras_id' : 
                (in_array('id_obra', $colunas) ? 'id_obra' : 
                (in_array('id', $colunas) ? 'id' : 'obras_id'));
                
    $coluna_nome = in_array('nome_obra', $colunas) ? 'nome_obra' : 
                  (in_array('nome', $colunas) ? 'nome' : 'nome_obra');
                  
    $coluna_endereco = in_array('endereço', $colunas) ? 'endereço' : 
                      (in_array('endereco', $colunas) ? 'endereco' : 
                      (in_array('localizacao', $colunas) ? 'localizacao' : 'endereço'));
                      
    $coluna_data_inicio = in_array('data_inicio', $colunas) ? 'data_inicio' : 
                         (in_array('data_inicio_real', $colunas) ? 'data_inicio_real' : 'data_inicio');
                         
    $coluna_data_fim = in_array('data_fim', $colunas) ? 'data_fim' : 
                      (in_array('data_fim_prevista', $colunas) ? 'data_fim_prevista' : 
                      (in_array('prazo', $colunas) ? 'prazo' : 'data_fim'));
    
    echo "<h2>Nomes de colunas detectados:</h2>";
    echo "<ul>";
    echo "<li>ID: $coluna_id</li>";
    echo "<li>Nome: $coluna_nome</li>";
    echo "<li>Endereço: $coluna_endereco</li>";
    echo "<li>Data Início: $coluna_data_inicio</li>";
    echo "<li>Data Fim: $coluna_data_fim</li>";
    echo "</ul>";
    
    // Buscar todas as obras
    $query = "SELECT * FROM obras";
    $result = mysqli_query($conn, $query);
    $num_rows = mysqli_num_rows($result);
    
    if ($num_rows > 0) {
        echo "<h2>Obras encontradas ($num_rows):</h2>";
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Nome</th>";
        echo "<th>Endereço</th>";
        echo "<th>Data Início</th>";
        echo "<th>Data Fim</th>";
        echo "<th>Status</th>";
        echo "</tr>";
        
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . ($row[$coluna_id] ?? 'N/A') . "</td>";
            echo "<td>" . ($row[$coluna_nome] ?? 'N/A') . "</td>";
            echo "<td>" . ($row[$coluna_endereco] ?? 'N/A') . "</td>";
            echo "<td>" . (isset($row[$coluna_data_inicio]) ? date('d/m/Y', strtotime($row[$coluna_data_inicio])) : 'N/A') . "</td>";
            echo "<td>" . (isset($row[$coluna_data_fim]) ? date('d/m/Y', strtotime($row[$coluna_data_fim])) : 'N/A') . "</td>";
            echo "<td>" . ($row['status'] ?? 'Em andamento') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Testar a consulta usada na página principal
        echo "<h2>Teste da consulta usada na página principal:</h2>";
        $query_teste = "SELECT * FROM obras ORDER BY $coluna_data_inicio DESC";
        $result_teste = mysqli_query($conn, $query_teste);
        
        if (!$result_teste) {
            echo "<p style='color:red'>Erro na consulta: " . mysqli_error($conn) . "</p>";
        } else {
            $num_rows_teste = mysqli_num_rows($result_teste);
            echo "<p>A consulta retornou $num_rows_teste registros.</p>";
        }
    } else {
        echo "<p style='color:red'>Não foram encontradas obras na tabela.</p>";
        
        // Oferecer a opção de adicionar uma obra de teste
        echo "<h2>Adicionar obra de teste</h2>";
        echo "<form method='post' action=''>";
        echo "<input type='submit' name='adicionar_teste' value='Adicionar Obra de Teste'>";
        echo "</form>";
        
        if (isset($_POST['adicionar_teste'])) {
            $query = "INSERT INTO obras ($coluna_nome, $coluna_endereco, $coluna_data_inicio, status) 
                     VALUES ('Obra de Teste', 'Endereço de Teste', NOW(), 'Em andamento')";
            
            if (mysqli_query($conn, $query)) {
                echo "<p style='color:green'>Obra de teste adicionada com sucesso! Atualize a página para ver.</p>";
            } else {
                echo "<p style='color:red'>Erro ao adicionar obra de teste: " . mysqli_error($conn) . "</p>";
            }
        }
    }
} else {
    echo "<p style='color:red'>A tabela 'obras' não existe!</p>";
    
    // Oferecer a opção de criar a tabela
    echo "<h2>Criar tabela 'obras'</h2>";
    echo "<form method='post' action=''>";
    echo "<input type='submit' name='criar_tabela' value='Criar Tabela Obras'>";
    echo "</form>";
    
    if (isset($_POST['criar_tabela'])) {
        $query = "CREATE TABLE obras (
                  obras_id INT NOT NULL AUTO_INCREMENT,
                  nome_obra VARCHAR(255) NOT NULL,
                  endereço TEXT NOT NULL,
                  data_inicio DATE NOT NULL,
                  data_fim DATE NULL,
                  status VARCHAR(50) DEFAULT 'Em andamento',
                  PRIMARY KEY (obras_id)
                )";
        
        if (mysqli_query($conn, $query)) {
            echo "<p style='color:green'>Tabela 'obras' criada com sucesso! Atualize a página para ver.</p>";
        } else {
            echo "<p style='color:red'>Erro ao criar tabela: " . mysqli_error($conn) . "</p>";
        }
    }
}

echo "<p><a href='Projeto pag 2.php'>Voltar para a página de obras</a></p>";

// Fechar conexão
mysqli_close($conn);
?>