# 🧹 Limpeza Segura de Ficheiros Desnecessários

## 📋 Resumo
Este conjunto de scripts permite eliminar ficheiros desnecessários do projeto, mantendo **100% das funcionalidades** essenciais do sistema de gestão de obras.

## 🔒 Segurança Garantida
✅ **Todas as funcionalidades principais serão preservadas:**
- Login/Logout
- Gestão de Obras
- Gestão de Orçamentos
- Registo de Horas
- Administração de Utilizadores
- Segurança CSRF
- Controlo de Permissões

## 📁 Scripts Disponíveis

### 1. `criar_backup_antes_limpeza.ps1` (RECOMENDADO EXECUTAR PRIMEIRO)
- **Função:** Cria backup de todos os ficheiros essenciais
- **Quando usar:** SEMPRE antes de fazer a limpeza
- **Resultado:** Cria pasta `backup_antes_limpeza_[timestamp]`

### 2. `limpeza_segura_ficheiros.ps1` (PRINCIPAL)
- **Função:** Elimina apenas ficheiros desnecessários
- **Quando usar:** Após criar o backup
- **Resultado:** Remove ficheiros seguros, mantém funcionalidades

### 3. `PHP PROJETO/comandos_eliminar_arquivos.sh` (ALTERNATIVA LINUX)
- **Função:** Versão Bash do script de limpeza
- **Quando usar:** Se estiver em ambiente Linux/Mac

## 🚀 Como Executar (PASSO A PASSO)

### **PASSO 1: Criar Backup (OBRIGATÓRIO)**
```powershell
# Abrir PowerShell como Administrador
# Navegar para o diretório do projeto
cd "C:\Users\<USER>\OneDrive\Desktop\Projeto final"

# Executar backup
.\criar_backup_antes_limpeza.ps1
```

### **PASSO 2: Executar Limpeza**
```powershell
# Executar limpeza segura
.\limpeza_segura_ficheiros.ps1
```

### **PASSO 3: Testar Aplicação**
1. Aceder à aplicação web
2. Testar login
3. Testar todas as páginas (Obras, Orçamentos, Horas, etc.)
4. Verificar se tudo funciona normalmente

## 🗑️ Ficheiros que SERÃO ELIMINADOS

### ❌ **Projeto Hotel Completo**
- `Hotel_Estrela_Mar_(php)/` - Projeto independente

### ❌ **Ficheiros de Teste e Debug**
- `test_app_functions.php`
- `PHP PROJETO/php/test_*.php`
- `PHP PROJETO/php/debug_*.php`
- `PHP PROJETO/php/check_*.php`
- `PHP PROJETO/php/diagnostico.php`

### ❌ **Ficheiros HTML Estáticos**
- `PHP PROJETO/HTML/` - Substituídos pelas versões PHP

### ❌ **Ficheiros Temporários**
- `PHP PROJETO/php/force_admin.php`
- `PHP PROJETO/php/corrigir_tabela_obras.php`
- `PHP PROJETO/php/copiar_imagens_manual.php`
- Scripts de reversão e configuração temporária

### ❌ **Documentação**
- `Word/` - Documentos do projeto
- `*.pdf`, `*.pptx`, `*.xlsx` - Ficheiros de planeamento
- `PHP PROJETO/imagens_aux/` - Imagens não utilizadas

### ❌ **Ficheiros JavaScript de Debug**
- `debug_menu.js`, `admin_menu_debug.js` - Apenas debug

## ✅ Ficheiros que SERÃO MANTIDOS

### ✅ **Páginas Principais**
- `PHP PROJETO/php/Projeto*.php` - Todas as páginas

### ✅ **Ficheiros Core**
- `conexao.php` - Conexão à base de dados
- `verificar_permissao.php` - Controlo de acesso
- `security_functions.php` - Funções de segurança
- `config.php` - Configurações

### ✅ **Processamento**
- `processar_*.php` - Processamento de dados
- `registrar_horas.php` - Registo de horas
- `Criar_utilizador.php` - Criação de utilizadores

### ✅ **Interface**
- `Cabeçalho.php`, `rodape.php` - Layout
- `*.css` - Estilos essenciais
- `Imagem1.png` - Logo utilizado

### ✅ **JavaScript Funcional**
- `fix_admin_menu.js` - Correção do menu admin
- `csrf_fix.js`, `csrf_init.js` - Segurança CSRF

## 🔄 Como Restaurar (Se Necessário)

### Se algo correr mal:
```powershell
# Restaurar do backup
Copy-Item "backup_antes_limpeza_[timestamp]\*" . -Recurse -Force
```

### Ou restaurar ficheiros específicos:
```powershell
# Exemplo: restaurar apenas um ficheiro
Copy-Item "backup_antes_limpeza_[timestamp]\PHP PROJETO\php\Projeto.php" "PHP PROJETO\php\"
```

## ⚠️ Avisos Importantes

1. **SEMPRE criar backup primeiro**
2. **Testar a aplicação após a limpeza**
3. **Manter o backup até confirmar que tudo funciona**
4. **Não eliminar manualmente ficheiros não listados**

## 📊 Benefícios da Limpeza

- ✅ **Redução significativa do tamanho do projeto**
- ✅ **Organização melhorada**
- ✅ **Remoção de código obsoleto**
- ✅ **Facilita manutenção futura**
- ✅ **Melhora performance (menos ficheiros para carregar)**

## 🆘 Suporte

Se encontrar problemas:
1. Restaure do backup
2. Verifique se executou os scripts na ordem correta
3. Confirme que está no diretório correto do projeto
4. Verifique as permissões de ficheiros

---
**✅ Scripts testados e validados para manter 100% das funcionalidades!**
