<?php
session_start();
require_once 'conexao.php';

if (isset($_GET['id']) && !empty($_GET['id'])) {
    $conn = connectToDatabase();
    
    // Preparar e limpar os dados
    $material_id = mysqli_real_escape_string($conn, $_GET['id']);
    
    // Excluir o material
    $query = "DELETE FROM materiais WHERE material_id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "i", $material_id);
    
    if (mysqli_stmt_execute($stmt)) {
        $_SESSION['mensagem'] = "Material excluído com sucesso!";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao excluir material: " . mysqli_error($conn);
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    mysqli_stmt_close($stmt);
    mysqli_close($conn);
} else {
    $_SESSION['mensagem'] = "ID do material não fornecido";
    $_SESSION['tipo_mensagem'] = "danger";
}

// Redirecionar explicitamente para a página de obras
header("Location: Projeto pag 2.php");
exit();
?>
