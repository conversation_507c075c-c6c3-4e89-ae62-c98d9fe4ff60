<?php
/**
 * Arquivo com funções de segurança para a aplicação
 */

// Incluir arquivo de configuração
require_once 'config.php';

/**
 * Função para sanitizar entrada de texto
 * @param string $input Texto a ser sanitizado
 * @return string Texto sanitizado
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Função para sanitizar saída de texto
 * @param string $output Texto a ser sanitizado para saída
 * @return string Texto sanitizado
 */
function sanitizeOutput($output) {
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

/**
 * Função para validar data no formato YYYY-MM-DD
 * @param string $date Data a ser validada
 * @return bool|string Retorna a data se válida ou false se inválida
 */
function validateDate($date) {
    if (empty($date)) {
        return false;
    }
    
    // Verificar formato da data (YYYY-MM-DD)
    if (preg_match('/^(\d{4})-(\d{2})-(\d{2})$/', $date, $matches)) {
        $ano = (int)$matches[1];
        $mes = (int)$matches[2];
        $dia = (int)$matches[3];
        
        // Verificar se a data é válida e o ano está dentro de um intervalo razoável (1900-2100)
        if (checkdate($mes, $dia, $ano) && $ano >= 1900 && $ano <= 2100) {
            return $date;
        }
    }
    
    return false;
}

/**
 * Função para validar número
 * @param mixed $number Número a ser validado
 * @param float $min Valor mínimo (opcional)
 * @param float $max Valor máximo (opcional)
 * @return bool|float Retorna o número se válido ou false se inválido
 */
function validateNumber($number, $min = null, $max = null) {
    if (!is_numeric($number)) {
        return false;
    }
    
    $number = (float)$number;
    
    if ($min !== null && $number < $min) {
        return false;
    }
    
    if ($max !== null && $number > $max) {
        return false;
    }
    
    return $number;
}

/**
 * Função para fazer hash seguro de senha
 * @param string $password Senha em texto puro
 * @return string Senha com hash
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Função para verificar senha
 * @param string $password Senha em texto puro
 * @param string $hash Hash da senha armazenada
 * @return bool Retorna true se a senha estiver correta
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Função para atualizar senhas em texto puro para hash
 * @param mysqli $conn Conexão com o banco de dados
 * @param int $user_id ID do usuário
 * @param string $password Senha em texto puro
 * @return bool Retorna true se a senha foi atualizada com sucesso
 */
function updatePasswordToHash($conn, $user_id, $password) {
    $hashed_password = hashPassword($password);
    $update_query = "UPDATE utilizadores SET password_utilizador = ? WHERE id_utilizadores = ?";
    $update_stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($update_stmt, "si", $hashed_password, $user_id);
    $result = mysqli_stmt_execute($update_stmt);
    mysqli_stmt_close($update_stmt);
    
    return $result;
}

/**
 * Função para registrar tentativas de login
 * @param string $username Nome de usuário
 * @param bool $success Se o login foi bem-sucedido
 */
function logLoginAttempt($username, $success) {
    $log_file = __DIR__ . '/logs/login_attempts.log';
    $dir = dirname($log_file);
    
    // Criar diretório de logs se não existir
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $time = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'];
    $status = $success ? 'SUCCESS' : 'FAILURE';
    $log_entry = "[$time] IP: $ip | User: $username | Status: $status\n";
    
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}

/**
 * Função para gerar token CSRF
 * @return string Token CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Função para verificar token CSRF
 * @param string $token Token CSRF a ser verificado
 * @return bool Retorna true se o token for válido
 */
function verifyCSRFToken($token) {
    if (!isset($_SESSION['csrf_token'])) {
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}
?>


