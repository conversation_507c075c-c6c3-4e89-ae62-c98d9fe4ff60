-- <PERSON><PERSON><PERSON> Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- -----------------------------------------------------
-- Schema mydb
-- -----------------------------------------------------
-- -----------------------------------------------------
-- Schema controle_obras
-- -----------------------------------------------------
DROP SCHEMA IF EXISTS `controle_obras` ;

-- -----------------------------------------------------
-- Schema controle_obras
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `controle_obras` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ;
USE `controle_obras` ;

-- -----------------------------------------------------
-- Table `controle_obras`.`obras`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`obras` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`obras` (
  `obras_id` INT NOT NULL AUTO_INCREMENT,
  `nome_obra` VARCHAR(255) NOT NULL,
  `endereço` TEXT NOT NULL,
  `data_inicio` DATE NULL DEFAULT NULL,
  `data_fim` DATE NULL DEFAULT NULL,
  PRIMARY KEY (`obras_id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;


-- -----------------------------------------------------
-- Table `controle_obras`.`materiais`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`materiais` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`materiais` (
  `material_id` INT NOT NULL AUTO_INCREMENT,
  `nome` VARCHAR(255) NOT NULL,
  `quantidade` INT NOT NULL,
  `obras_id` INT NULL DEFAULT NULL,
  PRIMARY KEY (`material_id`),
  CONSTRAINT `materiais_ibfk_1`
    FOREIGN KEY (`obras_id`)
    REFERENCES `controle_obras`.`obras` (`obras_id`)
    ON DELETE SET NULL)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `obras_id` ON `controle_obras`.`materiais` (`obras_id` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `controle_obras`.`utilizadores`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`utilizadores` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`utilizadores` (
  `id_utilizadores` INT NOT NULL,
  `nome_utilizador` VARCHAR(45) NULL,
  `cargo_utilizador` VARCHAR(45) NULL,
  `email_utilizador` VARCHAR(45) NULL,
  `password_utilizador` VARCHAR(45) NULL,
  PRIMARY KEY (`id_utilizadores`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `controle_obras`.`pessoal`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `controle_obras`.`pessoal` ;

CREATE TABLE IF NOT EXISTS `controle_obras`.`pessoal` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `nome` VARCHAR(255) NOT NULL,
  `cargo` VARCHAR(100) NULL DEFAULT NULL,
  `obras_id` INT NULL DEFAULT NULL,
  `utilizadores_id_utilizadores` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `pessoal_ibfk_1`
    FOREIGN KEY (`obras_id`)
    REFERENCES `controle_obras`.`obras` (`obras_id`)
    ON DELETE SET NULL,
  CONSTRAINT `fk_pessoal_utilizadores1`
    FOREIGN KEY (`utilizadores_id_utilizadores`)
    REFERENCES `controle_obras`.`utilizadores` (`id_utilizadores`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `obras_id` ON `controle_obras`.`pessoal` (`obras_id` ASC) VISIBLE;

CREATE INDEX `fk_pessoal_utilizadores1_idx` ON `controle_obras`.`pessoal` (`utilizadores_id_utilizadores` ASC) VISIBLE;


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
