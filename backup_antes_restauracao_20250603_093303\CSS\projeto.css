/* Paleta de cores consistente para todas as páginas */
:root {
    --primary-color: #4C4C4C;
    --secondary-color: #333333;
    --accent-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --border-color: #dee2e6;
    --background-color: #f5f5f5;
}

/* Estilos gerais para todas as páginas */
body {
    background-color: var(--background-color);
    color: var(--dark-color);
    font-family: 'Arial', sans-serif;
}

.wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content {
    flex: 1;
    padding: 20px 0;
}

/* Estilos para o menu de navegação */
.Menu {
    background-color: var(--primary-color);
    padding: 10px 0;
}

.Menu ul {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.<PERSON>u li {
    margin: 0 15px;
}

.Menu a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    text-transform: uppercase;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.Menu a:hover, .Menu a.active {
    background-color: var(--accent-color);
    border-radius: 4px;
}

.logotipo {
    max-height: 50px;
    margin-right: 10px;
}

/* Estilos para cabeçalhos */
h1, h2, h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

/* Estilos para botões */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #4C4C4C;
    border-color: #4C4C4C;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #4C4C4C;
    border-color: #4C4C4C;
}

/* Estilos para cards */
.obra-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
}

.obra-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Estilos para alertas */
.alert-info {
    background-color: #e8f4f8;
    border-color: #d6e9f9;
    color: #4C4C4C;
}

/* Estilos para modais */
.modal-header {
    background-color: var(--primary-color);
    color: white;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* Estilos para formulários */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(76, 76, 76, 0.25);
}

/* Estilos para badges */
.badge {
    padding: 5px 10px;
    font-weight: normal;
}

/* Estilos para a página de Gestão de Obras (Projeto pag 2.php) */
.page-header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
    color: white;
    margin-bottom: 5px;
}

.page-header p {
    opacity: 0.8;
    margin-bottom: 0;
}

.action-buttons {
    margin-bottom: 20px;
}

.action-buttons .btn {
    margin-right: 10px;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
}

.obras-table {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.obras-table .table {
    margin-bottom: 0;
}

.obras-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
    border: none;
}

.obras-table td {
    vertical-align: middle;
}

.obras-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.action-cell {
    width: 180px;
    text-align: center;
}

.action-cell .btn {
    margin: 0 2px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: normal;
    font-size: 0.85rem;
}

/* Estilos para os modais */
.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 0;
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 15px;
}

/* Estilos para formulários */
.form-label {
    font-weight: 500;
    margin-bottom: 5px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(76, 76, 76, 0.25);
}

/* Estilos para alertas */
.alert-info {
    background-color: #e8f4f8;
    border-color: #d6e9f9;
    color: #0c5460;
}

/* Estilos responsivos */
@media (max-width: 768px) {
    .obras-container {
        grid-template-columns: 1fr;
    }
    
    .Menu ul {
        flex-direction: column;
    }
    
    .Menu li {
        margin-right: 0;
        margin-bottom: 10px;
    }
}


/* Centralizar texto em parágrafos e cabeçalhos */
p, h1, h2, h3, h4, h5, h6 {
    text-align: center;
}

/* Centralizar texto em formulários */
.form-label, 
.form-control, 
.form-select {
    text-align: center;
}

/* Ajustar alinhamento de botões para centralizar */
.btn-container,
.obra-actions,
.form-actions {
    display: flex;
    gap: 10px;
}


/* Ajustar alinhamento de listas */
ul, ol {
    display: inline-block;
    text-align: left; /* Manter o texto da lista alinhado à esquerda para legibilidade */
}

/* Exceção para o menu de navegação que deve manter seu layout original */
.nav-container ul {
    display: flex;
    text-align: center;
}

/* Centralizar conteúdo em modais */
.modal-content {
    text-align: center;
}

/* Ajustar inputs de formulário para centralizar texto */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="date"],
textarea,
select {
    text-align: center;
}

/* Centralização específica para a página de Gestão de Obras (Projeto pag 2.php) */
.obras-container {
    text-align: center;
}

.obra-card {
    text-align: center;
    margin: 0 auto;
}

.obra-card h3,
.obra-card p,
.obra-card .badge,
.obra-card .obra-actions {
    text-align: center;
}

/* Forçar centralização para elementos específicos */
.row, .col-md-6, .col-md-12 {
    text-align: center;
}

.lead {
    text-align: center;
    width: 100%;
}

/* Centralizar cabeçalhos e textos em todas as páginas */
h1, h2, h3, h4, h5, h6, p, span, div {
    text-align: center;
}

/* Centralizar conteúdo de alertas */
.alert {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

/* Centralizar botões em containers */
.text-end {
    text-align: center !important;
}

/* Centralizar conteúdo em colunas */
.col-md-6 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Ajustar layout de linhas para centralizar */
.row {
    display: flex;
    justify-content: center;
}

/* Garantir que os botões de ação estejam centralizados */
.obra-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
}

/* Centralizar conteúdo em modais */
.modal-body form {
    text-align: center;
}

.modal-body .row {
    justify-content: center;
}

/* Ajustar layout para dispositivos móveis */
@media (max-width: 768px) {
    .col-md-6 {
        width: 100%;
        text-align: center;
    }
    
    .text-end {
        text-align: center !important;
    }
}

/* Estilo para a imagem principal da página inicial */
.imagem-principal {
    max-width: 100%;  /* Aumentado para 100% para tornar a imagem maior */
    height: auto;
    margin: 20px auto;
    display: block;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.imagem-principal:hover {
    transform: scale(1.02);
}

@media (max-width: 768px) {
    .imagem-principal {
        max-width: 95%;
    }
}

/* Estilo para o botão de atualização */
.refresh-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.refresh-btn i {
    font-size: 1.2rem;
}

.refresh-btn:hover {
    transform: rotate(180deg);
    background-color: #0d6efd;
    color: white;
}
