<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Verificar se a tabela obras existe
$tableExists = false;
$result = mysqli_query($conn, "SHOW TABLES LIKE 'obras'");
if ($result) {
    $tableExists = mysqli_num_rows($result) > 0;
}

echo "<h1>Verificação da Tabela Obras</h1>";

if (!$tableExists) {
    echo "<p style='color: red;'>A tabela 'obras' não existe no banco de dados!</p>";
    
    // Criar a tabela
    if (isset($_GET['criar']) && $_GET['criar'] == 'sim') {
        $query = "CREATE TABLE obras (
            obras_id INT AUTO_INCREMENT PRIMARY KEY,
            nome_obra VARCHAR(255) NOT NULL,
            endereço VARCHAR(255) NOT NULL,
            data_inicio DATE NOT NULL,
            data_fim DATE NULL,
            status VARCHAR(50) DEFAULT 'Em andamento',
            descricao TEXT NULL,
            orcamento DECIMAL(15, 2) NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        if (mysqli_query($conn, $query)) {
            echo "<p style='color: green;'>Tabela 'obras' criada com sucesso!</p>";
            echo "<p><a href='verificar_tabela_obras.php'>Atualizar</a></p>";
        } else {
            echo "<p style='color: red;'>Erro ao criar tabela: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p><a href='verificar_tabela_obras.php?criar=sim'>Criar tabela obras</a></p>";
    }
} else {
    echo "<p style='color: green;'>A tabela 'obras' existe no banco de dados.</p>";
    
    // Verificar a estrutura da tabela
    $result = mysqli_query($conn, "DESCRIBE obras");
    
    echo "<h2>Estrutura da Tabela</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    
    $colunas = [];
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
        
        $colunas[] = $row['Field'];
    }
    echo "</table>";
    
    // Verificar colunas necessárias
    $colunasNecessarias = ['obras_id', 'nome_obra', 'endereço', 'data_inicio', 'data_fim', 'status'];
    $colunasAusentes = [];
    
    foreach ($colunasNecessarias as $coluna) {
        if (!in_array($coluna, $colunas) && 
            !in_array(str_replace('obras_id', 'id_obra', $coluna), $colunas) && 
            !in_array(str_replace('endereço', 'endereco', $coluna), $colunas) && 
            !in_array(str_replace('endereço', 'localizacao', $coluna), $colunas)) {
            $colunasAusentes[] = $coluna;
        }
    }
    
    if (!empty($colunasAusentes)) {
        echo "<p style='color: red;'>Colunas ausentes: " . implode(", ", $colunasAusentes) . "</p>";
        
        // Adicionar colunas ausentes
        if (isset($_GET['corrigir']) && $_GET['corrigir'] == 'sim') {
            $alteracoes = [];
            
            foreach ($colunasAusentes as $coluna) {
                switch ($coluna) {
                    case 'obras_id':
                        $alteracoes[] = "ADD COLUMN obras_id INT AUTO_INCREMENT PRIMARY KEY";
                        break;
                    case 'nome_obra':
                        $alteracoes[] = "ADD COLUMN nome_obra VARCHAR(255) NOT NULL";
                        break;
                    case 'endereço':
                        $alteracoes[] = "ADD COLUMN endereço VARCHAR(255) NOT NULL";
                        break;
                    case 'data_inicio':
                        $alteracoes[] = "ADD COLUMN data_inicio DATE NOT NULL";
                        break;
                    case 'data_fim':
                        $alteracoes[] = "ADD COLUMN data_fim DATE NULL";
                        break;
                    case 'status':
                        $alteracoes[] = "ADD COLUMN status VARCHAR(50) DEFAULT 'Em andamento'";
                        break;
                }
            }
            
            if (!empty($alteracoes)) {
                $query = "ALTER TABLE obras " . implode(", ", $alteracoes);
                
                if (mysqli_query($conn, $query)) {
                    echo "<p style='color: green;'>Colunas adicionadas com sucesso!</p>";
                    echo "<p><a href='verificar_tabela_obras.php'>Atualizar</a></p>";
                } else {
                    echo "<p style='color: red;'>Erro ao adicionar colunas: " . mysqli_error($conn) . "</p>";
                }
            }
        } else {
            echo "<p><a href='verificar_tabela_obras.php?corrigir=sim'>Adicionar colunas ausentes</a></p>";
        }
    } else {
        echo "<p style='color: green;'>Todas as colunas necessárias estão presentes.</p>";
    }
    
    // Verificar registros na tabela
    $result = mysqli_query($conn, "SELECT * FROM obras");
    $numRegistros = mysqli_num_rows($result);
    
    echo "<h2>Registros na Tabela</h2>";
    echo "<p>Total de registros: $numRegistros</p>";
    
    if ($numRegistros > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        
        // Cabeçalhos da tabela
        $row = mysqli_fetch_assoc($result);
        foreach ($row as $key => $value) {
            echo "<th>$key</th>";
        }
        echo "</tr>";
        
        // Resetar o ponteiro do resultado
        mysqli_data_seek($result, 0);
        
        // Dados da tabela
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Nenhum registro encontrado na tabela.</p>";
        
        // Adicionar registro de teste
        if (isset($_GET['adicionar']) && $_GET['adicionar'] == 'sim') {
            $query = "INSERT INTO obras (nome_obra, endereço, data_inicio, status) 
                      VALUES ('Obra de Teste', 'Endereço de Teste', NOW(), 'Em andamento')";
            
            if (mysqli_query($conn, $query)) {
                echo "<p style='color: green;'>Registro de teste adicionado com sucesso!</p>";
                echo "<p><a href='verificar_tabela_obras.php'>Atualizar</a></p>";
            } else {
                echo "<p style='color: red;'>Erro ao adicionar registro de teste: " . mysqli_error($conn) . "</p>";
            }
        } else {
            echo "<p><a href='verificar_tabela_obras.php?adicionar=sim'>Adicionar registro de teste</a></p>";
        }
    }
}

// Fechar conexão
mysqli_close($conn);

echo "<p><a href='Projeto pag 2.php'>Voltar para a página de obras</a></p>";
?>
