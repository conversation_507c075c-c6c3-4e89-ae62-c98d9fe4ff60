<?php
session_start();

// Incluir arquivos necessários
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';
require_once 'conexao.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário tem permissão (apenas nível 1 - administrador)
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')) {
    $_SESSION['AccessError'] = "Acesso negado. Apenas administradores podem acessar esta página.";
    header("Location: Projeto.php");
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Buscar todos os utilizadores
$query = "SELECT id_utilizadores, nome_utilizador, email_utilizador, cargo_utilizador 
          FROM utilizadores 
          ORDER BY nome_utilizador";
$result = mysqli_query($conn, $query);

// Verificar se há utilizadores
$num_utilizadores = mysqli_num_rows($result);
?>

<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Listar Utilizadores - Built Organizer</title>
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu" class="active">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content" style="padding-top: 80px;">
            <div class="container">
                <!-- Mensagens de alerta -->
                <?php if(isset($_SESSION['mensagem'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['mensagem']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    </div>
                    <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
                <?php endif; ?>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h2>Gestão de Utilizadores</h2>
                                <a href="Registar_utilizador.php" class="btn btn-primary">
                                    <i class="bi bi-person-plus"></i> Novo Utilizador
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if($num_utilizadores > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Nome</th>
                                                    <th>Email</th>
                                                    <th>Nível de Acesso</th>
                                                    <th>Ações</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while($utilizador = mysqli_fetch_assoc($result)): ?>
                                                    <tr>
                                                        <td><?php echo $utilizador['id_utilizadores']; ?></td>
                                                        <td><?php echo htmlspecialchars($utilizador['nome_utilizador']); ?></td>
                                                        <td><?php echo htmlspecialchars($utilizador['email_utilizador']); ?></td>
                                                        <td>
                                                            <?php 
                                                                $cargo = $utilizador['cargo_utilizador'];
                                                                switch($cargo) {
                                                                    case '1':
                                                                        echo '<span class="badge bg-danger">Administrador</span>';
                                                                        break;
                                                                    case '2':
                                                                        echo '<span class="badge bg-warning">Gestor</span>';
                                                                        break;
                                                                    case '3':
                                                                        echo '<span class="badge bg-info">Supervisor</span>';
                                                                        break;
                                                                    case '4':
                                                                        echo '<span class="badge bg-secondary">Trabalhador</span>';
                                                                        break;
                                                                    default:
                                                                        echo '<span class="badge bg-light text-dark">Desconhecido</span>';
                                                                }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <?php if($utilizador['id_utilizadores'] != $_SESSION['user_id']): ?>
                                                                <button type="button" class="btn btn-danger btn-sm" 
                                                                        onclick="confirmarExclusao(<?php echo $utilizador['id_utilizadores']; ?>, '<?php echo htmlspecialchars($utilizador['nome_utilizador']); ?>')">
                                                                    <i class="bi bi-trash"></i> Eliminar
                                                                </button>
                                                            <?php else: ?>
                                                                <span class="badge bg-info">Utilizador atual</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> Nenhum utilizador encontrado.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2024 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div class="modal fade" id="modalConfirmacao" tabindex="-1" aria-labelledby="modalConfirmacaoLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalConfirmacaoLabel">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja eliminar o utilizador <strong id="nomeUtilizador"></strong>?</p>
                    <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> Esta ação não pode ser desfeita!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <a href="#" id="btnConfirmarExclusao" class="btn btn-danger">Eliminar</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>
    <!-- Script para depuração do menu de administração -->
    <script src="admin_menu_debug.js"></script>

    <!-- Script para confirmação de exclusão -->
    <script>
        function confirmarExclusao(id, nome) {
            document.getElementById('nomeUtilizador').textContent = nome;
            document.getElementById('btnConfirmarExclusao').href = 'eliminar_utilizador.php?id=' + id + '&csrf_token=<?php echo generateCSRFToken(); ?>';
            
            var modal = new bootstrap.Modal(document.getElementById('modalConfirmacao'));
            modal.show();
        }
    </script>
</body>
</html>

<?php mysqli_close($conn); ?>




