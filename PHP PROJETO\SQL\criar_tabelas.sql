-- Criar tabela de obras se não existir
CREATE TABLE IF NOT EXISTS obras (
    id_obra INT AUTO_INCREMENT PRIMARY KEY,
    nome_obra VARCHAR(255) NOT NULL,
    localizacao VARCHAR(255) NOT NULL,
    data_inicio DATE NOT NULL,
    data_fim_prevista DATE,
    data_fim_real DATE,
    orcamento DECIMAL(15, 2) NOT NULL,
    descricao TEXT,
    status VARCHAR(50) DEFAULT 'Em andamento',
    id_responsavel INT,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_responsavel) REFERENCES utilizadores(id_utilizadores)
);

-- Criar tabela de registro de horas se não existir
CREATE TABLE IF NOT EXISTS registro_horas (
    id_registro INT AUTO_INCREMENT PRIMARY KEY,
    id_obra INT NOT NULL,
    id_usuario INT NOT NULL,
    horas DECIMAL(8, 2) NOT NULL,
    data_registro DATE NOT NULL,
    descricao TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_obra) REFERENCES obras(id_obra),
    FOREIGN KEY (id_usuario) REFERENCES utilizadores(id_utilizadores)
);

-- Inserir algumas obras de exemplo se a tabela estiver vazia
INSERT INTO obras (nome_obra, localizacao, data_inicio, data_fim_prevista, orcamento, descricao, status, id_responsavel)
SELECT * FROM (
    SELECT 'Construção de Prédio Residencial', 'Lisboa', '2023-01-15', '2024-06-30', 1500000.00, 'Construção de prédio residencial com 20 apartamentos', 'Em andamento', 1
) AS tmp
WHERE NOT EXISTS (
    SELECT nome_obra FROM obras WHERE nome_obra = 'Construção de Prédio Residencial'
) LIMIT 1;

INSERT INTO obras (nome_obra, localizacao, data_inicio, data_fim_prevista, orcamento, descricao, status, id_responsavel)
SELECT * FROM (
    SELECT 'Reforma de Escritório', 'Porto', '2023-03-10', '2023-09-15', 350000.00, 'Reforma completa de escritório comercial', 'Concluída', 1
) AS tmp
WHERE NOT EXISTS (
    SELECT nome_obra FROM obras WHERE nome_obra = 'Reforma de Escritório'
) LIMIT 1;

INSERT INTO obras (nome_obra, localizacao, data_inicio, data_fim_prevista, orcamento, descricao, status, id_responsavel)
SELECT * FROM (
    SELECT 'Construção de Moradia', 'Faro', '2023-05-20', '2024-02-28', 750000.00, 'Construção de moradia de luxo com 5 quartos', 'Em andamento', 1
) AS tmp
WHERE NOT EXISTS (
    SELECT nome_obra FROM obras WHERE nome_obra = 'Construção de Moradia'
) LIMIT 1;
