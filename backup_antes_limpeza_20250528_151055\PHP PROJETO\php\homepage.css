/* Estilos para a página inicial do Built Organizer */

/* Importação de fontes do Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap');

/* Reset e estilos gerais */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin-bottom: 0.5rem;
    font-weight: 600;
    line-height: 1.2;
}

/* Estilos para o Menu */
.Menu {
    background-color: #4C4C4C;
    color: white;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
}

.Menu nav {
    width: 100%;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: center; /* Centraliza horizontalmente */
    padding: 10px 20px;
}

.Menu ul {
    list-style: none;
    padding: 0;
    margin: 0 auto; /* Centraliza a lista */
    display: flex;
    align-items: center;
    justify-content: center; /* Centraliza os itens */
    width: 100%;
    max-width: 1200px; /* Limita a largura para melhor controle */
}

.Menu li {
    padding: 0 20px; /* Aumenta o espaçamento horizontal */
    display: inline-block;
    text-align: center;
    margin: 0 5px; /* Adiciona margem entre os itens */
}

.Menu li.logo-item {
    margin-right: 20px;
}

.Menu .logotipo {
    max-height: 40px;
    width: auto;
}

.Menu a {
    color: white;
    text-decoration: none;
    display: block;
    font-weight: 500;
    transition: all 0.3s;
    padding: 10px 0;
    text-align: center;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.Menu a:hover {
    color: #ddd;
}

.Menu a.active {
    color: #fff;
    border-bottom: 2px solid white;
}

.welcome-text {
    color: #fff;
    font-size: 0.9em;
    white-space: nowrap;
    margin-left: auto; /* Empurra para a direita */
}

/* Estilos para o conteúdo principal */
.wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.content {
    flex: 1;
    padding: 80px 20px 20px 20px; /* Adiciona padding no topo para compensar o menu fixo */
}

/* Estilos para parágrafos */

/* Estilos para seções */

p {
    margin-bottom: 1rem;
}

/* Hero Section */
.hero-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 3rem;
    margin-top: 60px; /* Adiciona margem no topo para compensar o menu fixo */
}

.hero-content {
    flex: 1;
    max-width: 600px;
    padding-right: 2rem;
}

.hero-content h1 {
    font-size: 2.5rem;
    color: #4C4C4C;
    margin-bottom: 0.5rem;
}

.hero-content h2 {
    font-size: 1.8rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.hero-btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    background-color: #4C4C4C;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-btn:hover {
    background-color: #333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.hero-image img:hover {
    transform: scale(1.02);
}

.hero-features {
    display: flex;
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #4C4C4C;
    padding: 1rem;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Features Section */
.features-section {
    padding: 4rem 2rem;
    text-align: center;
    margin-bottom: 3rem;
}

.features-section h2 {
    font-size: 2rem;
    color: #4C4C4C;
    margin-bottom: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.feature-card .feature-icon {
    font-size: 2.5rem;
    color: #4C4C4C;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    color: #4C4C4C;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #6c757d;
}

/* Testimonials Section */
.testimonials-section {
    padding: 4rem 2rem;
    background-color: #f1f3f5;
    text-align: center;
    margin-bottom: 3rem;
    border-radius: 20px;
}

.testimonials-section h2 {
    font-size: 2rem;
    color: #4C4C4C;
    margin-bottom: 2rem;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.testimonial-card {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: left;
    transition: all 0.3s ease; /* Adiciona transição suave */
}

.testimonial-card:hover {
    transform: translateY(-5px); /* Move o card para cima ao passar o mouse */
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1); /* Aumenta a sombra para dar destaque */
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-style: italic;
    color: #6c757d;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-icon {
    font-size: 2rem;
    color: #4C4C4C;
    margin-right: 1rem;
}

.testimonial-info h4 {
    font-size: 1.1rem;
    color: #4C4C4C;
    margin-bottom: 0.2rem;
}

.testimonial-info p {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Call to Action Section */
.cta-section {
    padding: 4rem 2rem;
    text-align: center;
    background-color: #4C4C4C;
    color: white;
    border-radius: 20px;
    margin-bottom: 3rem;
}

.cta-section h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.1rem;
    max-width: 800px;
    margin: 0 auto 2rem;
}

.cta-btn {
    display: inline-block;
    padding: 1rem 2rem;
    background-color: white;
    color: #4C4C4C;
    text-decoration: none;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cta-btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-section {
        flex-direction: column;
        text-align: center;
        padding: 3rem 1.5rem;
    }

    .hero-content {
        max-width: 100%;
        padding-right: 0;
        margin-bottom: 2rem;
    }

    .hero-features {
        justify-content: center;
    }

    /* Menu responsivo */
    .Menu ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    .Menu li {
        padding: 5px 10px;
    }

    .Menu li.logo-item {
        width: 100%;
        text-align: center;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .content {
        padding-top: 120px; /* Aumenta o padding para compensar o menu maior */
    }

    .hero-section {
        margin-top: 100px; /* Aumenta a margem para compensar o menu maior */
    }
}

@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content h2 {
        font-size: 1.5rem;
    }

    .features-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
    }

    /* Menu responsivo para telas menores */
    .Menu {
        position: relative;
    }

    .Menu ul {
        flex-direction: column;
        align-items: center;
    }

    .Menu li {
        width: 100%;
        text-align: center;
        padding: 8px 0;
    }

    .Menu .logotipo {
        max-height: 35px;
    }

    .content {
        padding-top: 200px; /* Aumenta ainda mais o padding para o menu em coluna */
    }

    .hero-section {
        margin-top: 180px; /* Aumenta ainda mais a margem para o menu em coluna */
    }
}

/* Estilos para tabelas */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #4C4C4C;
    color: white;
    padding: 12px 15px;
    font-weight: 600;
    text-align: left;
    border: none;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Estilos para células de ação */
.action-cell {
    white-space: nowrap;
    text-align: center;
}

/* Estilos para botões de ação */
.btn {
    font-family: 'Poppins', sans-serif;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-view, .btn-info {
    background-color: #4C4C4C !important;
    color: white !important;
    border-color: #4C4C4C !important;
}

.btn-view:hover, .btn-info:hover {
    background-color: #3a3a3a !important;
    border-color: #3a3a3a !important;
}

.btn-edit, .btn-warning {
    background-color: #FFA500 !important;
    color: white !important;
    border-color: #FFA500 !important;
}

.btn-edit:hover, .btn-warning:hover {
    background-color: #FF8C00 !important;
    border-color: #FF8C00 !important;
}

.btn-delete, .btn-danger {
    background-color: #DC3545 !important;
    color: white !important;
    border-color: #DC3545 !important;
}

.btn-delete:hover, .btn-danger:hover {
    background-color: #C82333 !important;
    border-color: #C82333 !important;
}

.btn-add, .btn-success {
    background-color: #28A745 !important;
    color: white !important;
    border-color: #28A745 !important;
}

.btn-add:hover, .btn-success:hover {
    background-color: #218838 !important;
    border-color: #218838 !important;
}

/* Estilos para badges de status */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
}

.bg-success {
    background-color: #28A745 !important;
    color: white !important;
}

.bg-warning {
    background-color: #FFC107 !important;
    color: #212529 !important;
}

.bg-danger {
    background-color: #DC3545 !important;
    color: white !important;
}

.bg-info {
    background-color: #17A2B8 !important;
    color: white !important;
}

.bg-secondary {
    background-color: #6C757D !important;
    color: white !important;
}


