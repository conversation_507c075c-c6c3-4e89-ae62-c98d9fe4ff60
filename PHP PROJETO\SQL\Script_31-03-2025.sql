-- My<PERSON><PERSON> Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIG<PERSON>_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- -----------------------------------------------------
-- Schema mydb
-- -----------------------------------------------------
-- -----------------------------------------------------
-- Schema controle_obras
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema controle_obras
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `controle_obras` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ;
USE `controle_obras` ;

-- -----------------------------------------------------
-- Table `controle_obras`.`cargos`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `controle_obras`.`cargos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `cargo` VARCHAR(100) NOT NULL,
  `nivel_acesso` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;


-- -----------------------------------------------------
-- Table `controle_obras`.`obras`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `controle_obras`.`obras` (
  `obras_id` INT NOT NULL AUTO_INCREMENT,
  `nome_obra` VARCHAR(255) NOT NULL,
  `endereço` TEXT NOT NULL,
  `data_inicio` DATE NOT NULL,
  `data_fim` DATE NOT NULL,
  PRIMARY KEY (`obras_id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;


-- -----------------------------------------------------
-- Table `controle_obras`.`materiais`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `controle_obras`.`materiais` (
  `material_id` INT NOT NULL AUTO_INCREMENT,
  `nome` VARCHAR(255) NOT NULL,
  `quantidade` INT NOT NULL,
  `obras_id` INT NULL DEFAULT NULL,
  PRIMARY KEY (`material_id`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `obras_id` ON `controle_obras`.`materiais` (`obras_id` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `controle_obras`.`utilizadores`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `controle_obras`.`utilizadores` (
  `id_utilizadores` INT NOT NULL AUTO_INCREMENT,
  `nome_utilizador` VARCHAR(45) NOT NULL,
  `email_utilizador` VARCHAR(45) NOT NULL,
  `password_utilizador` VARCHAR(45) NOT NULL,
  `cargo_utilizador` INT NULL DEFAULT NULL,
  PRIMARY KEY (`id_utilizadores`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `utilizadores_cargos_idx` ON `controle_obras`.`utilizadores` (`cargo_utilizador` ASC) VISIBLE;


-- -----------------------------------------------------
-- Table `controle_obras`.`servicos_obra`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `controle_obras`.`servicos_obra` (
  `id_servicos_obra` INT NOT NULL AUTO_INCREMENT,
  `id_utilizador` INT NULL DEFAULT NULL,
  `id_obra` INT NULL DEFAULT NULL,
  `id_materiais` INT NULL DEFAULT NULL,
  `qt_materiais` INT NULL DEFAULT NULL,
  `data` DATE NOT NULL,
  `total_hrs` INT NOT NULL,
  `servicos_obracol` VARCHAR(45) NULL,
  PRIMARY KEY (`id_servicos_obra`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_0900_ai_ci;

CREATE INDEX `obra_servicos_idx` ON `controle_obras`.`servicos_obra` (`id_obra` ASC) VISIBLE;

CREATE INDEX `servicos_utilizador_idx` ON `controle_obras`.`servicos_obra` (`id_utilizador` ASC) VISIBLE;

CREATE INDEX `servicos_materiais_idx` ON `controle_obras`.`servicos_obra` (`id_materiais` ASC) VISIBLE;


SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
