<?php
session_start();
require_once 'verificar_permissao.php';

echo "<h1>Debug da Sessão e Permissões</h1>";

echo "<h3>Informações da Sessão:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Verificação de Permissões:</h3>";

if (isset($_SESSION['cargo_utilizador'])) {
    $cargo = $_SESSION['cargo_utilizador'];
    echo "<p>Cargo do usuário: <strong>$cargo</strong></p>";
    
    // Testar permissões para diferentes menus
    $menus = ['OBRAS', 'ORCAMENTOS', 'HORAS', 'GESTAO', 'ADMINISTRACAO'];
    
    foreach ($menus as $menu) {
        $tem_acesso = checkMenuAccess($cargo, $menu);
        $status = $tem_acesso ? "✅ Permitido" : "❌ Negado";
        echo "<p>$menu: $status</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Cargo do usuário não definido na sessão</p>";
}

echo "<h3>Informações do Usuário:</h3>";
if (isset($_SESSION['userName'])) {
    echo "<p>Nome do usuário: <strong>" . $_SESSION['userName'] . "</strong></p>";
} else {
    echo "<p style='color: red;'>❌ Nome do usuário não definido na sessão</p>";
}

if (isset($_SESSION['id_utilizadores'])) {
    echo "<p>ID do usuário: <strong>" . $_SESSION['id_utilizadores'] . "</strong></p>";
} else {
    echo "<p style='color: red;'>❌ ID do usuário não definido na sessão</p>";
}

echo "<p><a href='Projeto pag 3.php'>← Voltar para a página de orçamentos</a></p>";
?>
