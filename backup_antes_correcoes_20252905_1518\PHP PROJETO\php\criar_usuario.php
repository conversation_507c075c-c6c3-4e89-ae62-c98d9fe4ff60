<?php
// Iniciar sessão
session_start();

// Incluir arquivo de verificação de permissão
require_once 'verificar_permissao.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    // Redirecionar para a página inicial com mensagem
    header("Location: Projeto.php?msg=" . urlencode("Você precisa estar logado para acessar esta página."));
    exit();
}

// Verificar se o usuário tem permissão (nível 1 ou 2)
if (!isset($_SESSION['cargo_utilizador']) || ($_SESSION['cargo_utilizador'] !== '1' && $_SESSION['cargo_utilizador'] !== '2')) {
    // Redirecionar para a página inicial com mensagem
    header("Location: Projeto.php?msg=" . urlencode("Você não tem permissão para acessar esta página."));
    exit();
}

// Obter o nome da página atual
$currentPage = basename($_SERVER['PHP_SELF']);

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar se todos os campos foram preenchidos
    if (
        isset($_POST['nome_utilizador']) && !empty($_POST['nome_utilizador']) &&
        isset($_POST['email_utilizador']) && !empty($_POST['email_utilizador']) &&
        isset($_POST['password_utilizador']) && !empty($_POST['password_utilizador']) &&
        isset($_POST['password_utilizador_confirma']) && !empty($_POST['password_utilizador_confirma']) &&
        isset($_POST['cargo_utilizador']) && !empty($_POST['cargo_utilizador'])
    ) {
        // Verificar se as senhas coincidem
        if ($_POST['password_utilizador'] !== $_POST['password_utilizador_confirma']) {
            $error = "As senhas não coincidem.";
        } else {
            // Conectar ao banco de dados
            require_once 'conexao.php';

            // Verificar se o usuário já existe
            $nome_utilizador = $_POST['nome_utilizador'];
            $email_utilizador = $_POST['email_utilizador'];

            $sql_check = "SELECT * FROM utilizadores WHERE nome_utilizador = ? OR email_utilizador = ?";
            $stmt_check = $conn->prepare($sql_check);
            $stmt_check->bind_param("ss", $nome_utilizador, $email_utilizador);
            $stmt_check->execute();
            $result_check = $stmt_check->get_result();

            if ($result_check->num_rows > 0) {
                $error = "Usuário ou email já existe.";
            } else {
                // Criptografar a senha
                $password_hash = password_hash($_POST['password_utilizador'], PASSWORD_DEFAULT);
                $cargo_utilizador = $_POST['cargo_utilizador'];

                // Inserir o novo usuário
                $sql_insert = "INSERT INTO utilizadores (nome_utilizador, email_utilizador, password_utilizador, cargo_utilizador) VALUES (?, ?, ?, ?)";
                $stmt_insert = $conn->prepare($sql_insert);
                $stmt_insert->bind_param("ssss", $nome_utilizador, $email_utilizador, $password_hash, $cargo_utilizador);

                if ($stmt_insert->execute()) {
                    $success = "Usuário criado com sucesso!";
                } else {
                    $error = "Erro ao criar usuário: " . $conn->error;
                }

                $stmt_insert->close();
            }

            $stmt_check->close();
            $conn->close();
        }
    } else {
        $error = "Todos os campos são obrigatórios.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar Usuário - Built Organizer</title>
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
    <style>
        .card {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: none;
            margin-top: 30px;
        }
        .card-header {
            background-color: #4C4C4C;
            color: white;
            padding: 15px;
        }
        .btn-primary {
            background-color: #4C4C4C;
            border-color: #4C4C4C;
        }
        .btn-primary:hover {
            background-color: #333;
            border-color: #333;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
                            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                                <li><a href="Projeto pag 2.php">OBRAS</a></li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && $_SESSION['cargo_utilizador'] === '1'): ?>
                            <li><a href="Registar_utilizador.php">ADMINISTRAÇÃO</a></li>
                        <?php endif; ?>

                        <?php if($currentPage == 'Projeto.php'): ?>
                            <li><a href="sobre_nos.php">SOBRE NÓS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
                            <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                            <li><span class="welcome-text">Bem vindo <?php echo $_SESSION['userName']; ?> (Nível: <?php echo $_SESSION['cargo_utilizador']; ?>)</span></li>
                        <?php else: ?>
                            <li><a href="#" id="login-btn">LOGIN</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content" style="padding-top: 80px;">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <?php if(isset($error)): ?>
                            <div class="alert alert-danger" role="alert">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if(isset($success)): ?>
                            <div class="alert alert-success" role="alert">
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <div class="card">
                            <div class="card-header">
                                <h2>Criar Novo Usuário</h2>
                                <p class="mb-0">Preencha o formulário para criar um novo usuário</p>
                            </div>
                            <div class="card-body">
                                <form action="criar_usuario.php" method="POST">
                                    <div class="mb-3">
                                        <label for="nome_utilizador" class="form-label">Nome de Usuário</label>
                                        <input type="text" class="form-control" id="nome_utilizador" name="nome_utilizador" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email_utilizador" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email_utilizador" name="email_utilizador" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password_utilizador" class="form-label">Senha</label>
                                        <input type="password" class="form-control" id="password_utilizador" name="password_utilizador" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password_utilizador_confirma" class="form-label">Confirmar Senha</label>
                                        <input type="password" class="form-control" id="password_utilizador_confirma" name="password_utilizador_confirma" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="cargo_utilizador" class="form-label">Nível de Acesso</label>
                                        <select class="form-select" id="cargo_utilizador" name="cargo_utilizador" required>
                                            <option value="">Selecione o nível</option>
                                            <?php if(isset($_SESSION['cargo_utilizador']) && $_SESSION['cargo_utilizador'] === '1'): ?>
                                                <option value="1">Nível 1 (Administrador)</option>
                                            <?php endif; ?>
                                            <option value="2">Nível 2 (Gestor)</option>
                                            <option value="3">Nível 3 (Supervisor)</option>
                                            <option value="4">Nível 4 (Trabalhador)</option>
                                        </select>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">Criar Usuário</button>
                                        <a href="Projeto.php" class="btn btn-secondary">Cancelar</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
