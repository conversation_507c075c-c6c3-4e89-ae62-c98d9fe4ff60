<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header("Location: Projeto.php?msg=" . urlencode("Você não tem permissão para acessar esta página"));
    exit();
}

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['mensagem'] = "ID do registro não fornecido.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

$id = mysqli_real_escape_string($conn, $_GET['id']);

// Verificar se o registro existe e obter informações
$query_check = "SELECT id_usuario FROM registro_horas WHERE id = ?";
$stmt_check = mysqli_prepare($conn, $query_check);
mysqli_stmt_bind_param($stmt_check, "i", $id);
mysqli_stmt_execute($stmt_check);
$result_check = mysqli_stmt_get_result($stmt_check);

if (mysqli_num_rows($result_check) == 0) {
    $_SESSION['mensagem'] = "Registro não encontrado.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

$registro = mysqli_fetch_assoc($result_check);

// Verificar permissão para excluir
// Administradores (1) e gerentes (2) podem excluir qualquer registro
// Outros usuários só podem excluir seus próprios registros
if ($_SESSION['cargo_utilizador'] != '1' && $_SESSION['cargo_utilizador'] != '2' && 
    $registro['id_usuario'] != $_SESSION['user_id']) {
    $_SESSION['mensagem'] = "Você não tem permissão para excluir este registro.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

// Excluir o registro
$query_delete = "DELETE FROM registro_horas WHERE id = ?";
$stmt_delete = mysqli_prepare($conn, $query_delete);
mysqli_stmt_bind_param($stmt_delete, "i", $id);

if (mysqli_stmt_execute($stmt_delete)) {
    $_SESSION['mensagem'] = "Registro excluído com sucesso!";
    $_SESSION['tipo_mensagem'] = "success";
} else {
    $_SESSION['mensagem'] = "Erro ao excluir registro: " . mysqli_error($conn);
    $_SESSION['tipo_mensagem'] = "danger";
}

mysqli_stmt_close($stmt_check);
mysqli_stmt_close($stmt_delete);
mysqli_close($conn);

// Redirecionar de volta para a página de horas
header("Location: Projeto pag 4.php");
exit();
?>






