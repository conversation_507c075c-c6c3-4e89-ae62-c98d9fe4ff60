<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

echo "<h1>Teste de Funcionalidade de Orçamentos</h1>";

// Conectar ao banco de dados
$conn = connectToDatabase();
if (!$conn) {
    echo "<p style='color: red;'>❌ Erro de conexão com o banco de dados: " . mysqli_connect_error() . "</p>";
    exit;
}
echo "<p style='color: green;'>✅ Conexão com banco de dados estabelecida</p>";

// Verificar se a tabela orcamentos existe
$result = mysqli_query($conn, "SHOW TABLES LIKE 'orcamentos'");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p style='color: green;'>✅ Tabela 'orcamentos' existe</p>";
    
    // Verificar estrutura da tabela
    $structure = mysqli_query($conn, "DESCRIBE orcamentos");
    echo "<h3>Estrutura da tabela 'orcamentos':</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    while ($row = mysqli_fetch_assoc($structure)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ Tabela 'orcamentos' não existe</p>";
    
    // Tentar criar a tabela
    $create_table_query = "
    CREATE TABLE IF NOT EXISTS `orcamentos` (
      `id` INT NOT NULL AUTO_INCREMENT,
      `obra_id` INT NOT NULL,
      `descricao` TEXT NOT NULL,
      `valor` DECIMAL(15, 2) NOT NULL,
      `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      `data_aprovacao` DATE NULL,
      `status` VARCHAR(50) DEFAULT 'Em análise',
      PRIMARY KEY (`id`),
      CONSTRAINT `fk_orcamentos_obras`
        FOREIGN KEY (`obra_id`)
        REFERENCES `obras` (`obras_id`)
        ON DELETE CASCADE
        ON UPDATE CASCADE
    )";
    
    if (mysqli_query($conn, $create_table_query)) {
        echo "<p style='color: green;'>✅ Tabela 'orcamentos' criada com sucesso</p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao criar tabela 'orcamentos': " . mysqli_error($conn) . "</p>";
    }
}

// Verificar se a tabela obras existe
$result_obras = mysqli_query($conn, "SHOW TABLES LIKE 'obras'");
if ($result_obras && mysqli_num_rows($result_obras) > 0) {
    echo "<p style='color: green;'>✅ Tabela 'obras' existe</p>";
    
    // Contar obras disponíveis
    $count_obras = mysqli_query($conn, "SELECT COUNT(*) as total FROM obras");
    $total_obras = mysqli_fetch_assoc($count_obras)['total'];
    echo "<p>📊 Total de obras cadastradas: $total_obras</p>";
    
    if ($total_obras == 0) {
        echo "<p style='color: orange;'>⚠️ Nenhuma obra cadastrada. É necessário cadastrar obras antes de criar orçamentos.</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Tabela 'obras' não existe</p>";
}

// Verificar funções de segurança
if (function_exists('generateCSRFToken')) {
    echo "<p style='color: green;'>✅ Função generateCSRFToken() disponível</p>";
} else {
    echo "<p style='color: red;'>❌ Função generateCSRFToken() não encontrada</p>";
}

if (function_exists('verifyCSRFToken')) {
    echo "<p style='color: green;'>✅ Função verifyCSRFToken() disponível</p>";
} else {
    echo "<p style='color: red;'>❌ Função verifyCSRFToken() não encontrada</p>";
}

// Verificar se os arquivos necessários existem
$arquivos_necessarios = [
    'processar_orcamento.php',
    'adicionar_orcamento.php',
    'Projeto pag 3.php',
    'conexao.php',
    'security_functions.php',
    'config.php'
];

echo "<h3>Verificação de arquivos:</h3>";
foreach ($arquivos_necessarios as $arquivo) {
    if (file_exists($arquivo)) {
        echo "<p style='color: green;'>✅ $arquivo existe</p>";
    } else {
        echo "<p style='color: red;'>❌ $arquivo não encontrado</p>";
    }
}

// Fechar conexão
mysqli_close($conn);

echo "<h3>Teste concluído!</h3>";
echo "<p><a href='Projeto pag 3.php'>← Voltar para a página de orçamentos</a></p>";
?>
