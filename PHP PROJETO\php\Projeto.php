<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar permissões para a página atual
$currentPage = basename($_SERVER['PHP_SELF']);
verificarPermissaoPagina($currentPage);

// Login verification
if(isset($_POST['username']) && isset($_POST['pass']) && isset($_POST['csrf_token'])) {
    // Verificar token CSRF
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['InvalidUser'] = "Erro de segurança. Por favor, tente novamente.";
        header('Location: Projeto.php');
        exit();
    }

    $conn = connectToDatabase();

    $user = mysqli_real_escape_string($conn, $_POST['username']);
    $pass = $_POST['pass']; // Não escapamos a senha aqui pois usaremos prepared statement

    // Registrar tentativa de login (para fins de segurança)
    logLoginAttempt($user, false); // Inicialmente marcamos como falha

    // Query para verificar o utilizador usando prepared statement
    $query_user = "SELECT id_utilizadores, nome_utilizador, email_utilizador, password_utilizador, cargo_utilizador
                   FROM utilizadores
                   WHERE nome_utilizador = ?";

    $stmt = mysqli_prepare($conn, $query_user);
    mysqli_stmt_bind_param($stmt, "s", $user);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if(mysqli_num_rows($result) == 1) {
        $user_data = mysqli_fetch_assoc($result);

        // Verificar a senha
        // Sistema híbrido que suporta tanto senhas com hash quanto senhas em texto puro (legado)
        if (verifyPassword($pass, $user_data['password_utilizador']) || $user_data['password_utilizador'] === $pass) {
            // Autenticação bem-sucedida
            $_SESSION['userName'] = $user_data['nome_utilizador'];
            $_SESSION['cargo_utilizador'] = $user_data['cargo_utilizador'];
            $_SESSION['user_id'] = $user_data['id_utilizadores'];
            $_SESSION['last_activity'] = time();
            $_SESSION['last_regeneration'] = time();

            // Regenerar ID da sessão após login bem-sucedido
            session_regenerate_id(true);

            // Se a senha ainda estiver em texto puro, atualizá-la para hash
            if ($user_data['password_utilizador'] === $pass) {
                updatePasswordToHash($conn, $user_data['id_utilizadores'], $pass);
                error_log("Senha atualizada para hash: Usuário {$user_data['nome_utilizador']}");
            }

            // Registrar login bem-sucedido
            logLoginAttempt($user, true);

            // Debug para verificar o cargo
            error_log("Login: Usuário {$user_data['nome_utilizador']} com cargo {$user_data['cargo_utilizador']}");
        } else {
            // Senha incorreta
            $_SESSION['InvalidUser'] = "Nome de utilizador ou senha incorretos";
        }
    } else {
        // Usuário não encontrado
        $_SESSION['InvalidUser'] = "Nome de utilizador ou senha incorretos";
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);
}

// Verificar se é uma ação de logout
if(isset($_GET['action']) && $_GET['action'] == 'logout') {
    // Limpar todas as variáveis de sessão
    $_SESSION = array();

    // Destruir o cookie da sessão se existir
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Destruir a sessão
    session_destroy();

    // Redirecionar para a página inicial
    header('Location: Projeto.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projeto Built Organizer</title>

    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!--CSS da biblioteca Bootstrap-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!--JS da biblioteca Bootstrap-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!--Icones da biblioteca Bootstrap-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
</head>
<body>
    <div class="wrapper">
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
                            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                                <li><a href="Projeto pag 2.php">OBRAS</a></li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>


                        <?php if($currentPage == 'Projeto.php'): ?>
                            <li><a href="sobre_nos.php">SOBRE NÓS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
                            <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                            <li><span class="welcome-text">Bem vindo <?php echo $_SESSION['userName']; ?> (Nível: <?php echo $_SESSION['cargo_utilizador']; ?>)</span></li>
                        <?php else: ?>
                            <li><a href="#" id="login-btn">LOGIN</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        </div>

        <!-- Links de depuração (apenas visíveis para desenvolvedores) -->
        <!-- Removido link de debug -->

        <?php if(isset($_SESSION['AdminSuccess'])): ?>
            <div style="margin: 20px auto; max-width: 800px; text-align: center; background-color: #4CAF50; color: white; padding: 15px; border-radius: 5px;">
                <p><?php echo $_SESSION['AdminSuccess']; ?></p>
                <?php unset($_SESSION['AdminSuccess']); ?>
            </div>
        <?php endif; ?>

        <?php if(isset($_SESSION['AccessError'])): ?>
            <div class="error-message">
                <p><?php echo $_SESSION['AccessError']; ?></p>
                <?php unset($_SESSION['AccessError']); ?>
            </div>
        <?php endif; ?>

        <?php if(isset($_GET['msg'])): ?>
            <div style="margin: 20px auto; max-width: 800px; text-align: center; background-color: #4C4C4C; color: white; padding: 15px; border-radius: 5px;">
                <p><?php echo $_GET['msg']; ?></p>
            </div>
        <?php endif; ?>

        <!-- Hero Section -->
        <div class="container-fluid p-0">
            <div class="hero-section">
                <div class="hero-content">
                    <h1>BUILT ORGANIZER</h1>
                    <h2>Gestão de Obras Simplificada</h2>
                    <p>A plataforma completa para gestão de projetos de construção, orçamentos e controle de horas.</p>
                    <?php if(!isset($_SESSION['userName']) || $_SESSION['userName'] == null): ?>
                        <!-- Botão "Começar Agora" removido conforme solicitado -->
                    <?php else: ?>
                        <!-- As caixas de ação foram removidas conforme solicitado -->
                    <?php endif; ?>
                </div>
                <div class="hero-image">
                    <img src="Imagem1.png" alt="BUILT ORGANIZER">
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="features-section">
            <h2>Recursos Principais</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="bi bi-building"></i></div>
                    <h3>Gestão de Obras</h3>
                    <p>Cadastre e gerencie todas as suas obras em um só lugar. Acompanhe o progresso e mantenha todos os detalhes organizados.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="bi bi-cash-coin"></i></div>
                    <h3>Controle de Orçamentos</h3>
                    <p>Crie e acompanhe orçamentos detalhados para cada projeto. Mantenha o controle financeiro de suas obras.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="bi bi-clock-history"></i></div>
                    <h3>Registo de Horas</h3>
                    <p>Registe as horas trabalhadas por funcionário e por obra. Otimize a alocação de recursos humanos.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="bi bi-people"></i></div>
                    <h3>Gestão de Equipes</h3>
                    <p>Organize suas equipes por projeto e controle níveis de acesso. Melhore a comunicação e a produtividade.</p>
                </div>
            </div>
        </div>

        <!-- Testimonials Section -->
        <div class="testimonials-section">
            <h2>O que Nossos Clientes Dizem</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"O Built Organizer revolucionou a forma como gerenciamos nossas obras. Economizamos tempo e recursos significativamente."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-icon"><i class="bi bi-person-circle"></i></div>
                        <div class="testimonial-info">
                            <h4>João Silva</h4>
                            <p>Engenheiro Civil</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"A facilidade de registrar horas e materiais diretamente no sistema tornou nosso trabalho muito mais eficiente."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-icon"><i class="bi bi-person-circle"></i></div>
                        <div class="testimonial-info">
                            <h4>Maria Oliveira</h4>
                            <p>Gestora de Projetos</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"O controle de orçamentos é excepcional. Consigo visualizar rapidamente o status financeiro de cada projeto."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-icon"><i class="bi bi-person-circle"></i></div>
                        <div class="testimonial-info">
                            <h4>António Santos</h4>
                            <p>Diretor Financeiro</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <h2 style="color:rgb(169, 181, 183); font-weight: bold;">Pronto para Otimizar a Gestão das Suas Obras?</h2>
            <p>Junte-se a centenas de empresas que já estão utilizando o Built Organizer para melhorar a eficiência e o controle de seus projetos.</p>
            <?php if(!isset($_SESSION['userName']) || $_SESSION['userName'] == null): ?>
                <!-- Botão "Começar Agora" da seção CTA removido conforme solicitado -->
            <?php else: ?>
                <!-- Botão "Ir para Obras" removido conforme solicitado -->
            <?php endif; ?>
        </div>
    </div>

    <!-- Formulário de Login -->
    <div class="login-container" id="login-container">
        <div class="formulario">
            <p class="cabecalho_formulario"><strong>Área Reservada</strong></p>
            <div class="conteiner" align="center">
                <form action="Projeto.php" method="post">
                    <!-- Token CSRF para segurança -->
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="mb-3" align="center">
                        <br>
                        <label for='user' class="from-label">Utilizador</label>
                        <input type="text" class="from-control" id="username" name="username"
                        aria-describedby="username" required>
                    </div>
                    <div class="mb-3" align="center">
                        <label for="pass" class="from-label">Password</label>
                        <input type="password" class="froma-control" id="pass" name="pass"
                        required>
                    </div>
                    <div class="mb-3 from-check" align="center">
                        <button type="submit" class="btn btn-primary">Entrar</button>
                        <button type="button" class="btn" id="cancel-login">Cancelar</button>
                    </div>
                </form>
            </div>
        </div>

        <h4 style="color: red; background-color: white;">
            <?php
            if (isset($_SESSION['InvalidUser']) != null){
                echo ("Erro: " . $_SESSION['InvalidUser']);
            }
            ?>
        </h4>
    </div>

    <!-- Rodapé -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">&copy; <?php echo date('Y'); ?> Built Organizer. Todos os direitos reservados.</p>
    </footer>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>

    <script>
        // Script para controlar a exibição do formulário de login
        document.addEventListener('DOMContentLoaded', function() {
            const loginBtn = document.getElementById('login-btn');
            const heroLoginBtn = document.getElementById('hero-login-btn');
            const ctaLoginBtn = document.getElementById('cta-login-btn');
            const loginContainer = document.getElementById('login-container');
            const cancelLogin = document.getElementById('cancel-login');

            // Função para mostrar o formulário de login
            function showLoginForm(e) {
                if (e) e.preventDefault();
                loginContainer.style.display = 'flex';
            }

            // Função para esconder o formulário de login
            function hideLoginForm() {
                loginContainer.style.display = 'none';
            }

            // Adicionar event listeners para todos os botões de login
            if (loginBtn) {
                loginBtn.addEventListener('click', showLoginForm);
            }

            if (heroLoginBtn) {
                heroLoginBtn.addEventListener('click', showLoginForm);
            }

            if (ctaLoginBtn) {
                ctaLoginBtn.addEventListener('click', showLoginForm);
            }

            if (cancelLogin) {
                cancelLogin.addEventListener('click', hideLoginForm);
            }

            // Fechar o formulário de login quando clicar fora dele
            loginContainer.addEventListener('click', function(e) {
                if (e.target === loginContainer) {
                    hideLoginForm();
                }
            });
        });
    </script>

</body>
</html>
