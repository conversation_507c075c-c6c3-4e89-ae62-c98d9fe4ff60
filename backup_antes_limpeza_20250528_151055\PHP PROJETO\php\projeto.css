/* Importação de fontes do Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap');

/* Variáveis de cores para consistência */
:root {
    --primary-color: #4C4C4C;
    --secondary-color: #333333;
    --accent-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --border-color: #dee2e6;
    --background-color: #f5f5f5;
}

/* Reset e estilos gerais */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}
body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    color: #333;
}

/* Estilos para o Header 1 */
.Header1, .Header {
    position: relative; /* Garante que o header não seja fixo */
    width: 100%;
    text-align: center;
    padding: 20px 0;
    background-color: var(--light-color);
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.Header1 img, .Header img {
    max-width: 100%;
    height: auto;
    margin: 0 auto; /* Centraliza a imagem */
    display: block;
}

/* Ajuste para o título principal */
h1 {
    font-size: 2.2rem;
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.5rem;
    text-align: center;
}

h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%; /* Centraliza a linha decorativa */
    transform: translateX(-50%); /* Ajusta para centralização perfeita */
    width: 50px;
    height: 3px;
    background-color: #4C4C4C;
}

h2 {
    font-size: 1.8rem;
    margin-bottom: 0.8rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 0.6rem;
}

/* Estilos para o Menu - Não fixo */
.Menu {
    background-color: #4C4C4C;
    color: white;
    width: 100%;
    position: relative; /* Alterado de fixed para relative para não fixar o menu */
    top: 0;
    left: 0;
    z-index: 100;
}

.Menu nav {
    width: 100%;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: center; /* Centraliza horizontalmente */
    padding: 10px 20px;
}

.Menu ul {
    list-style: none;
    padding: 0;
    margin: 0 auto; /* Centraliza a lista */
    display: flex;
    align-items: center;
    justify-content: center; /* Centraliza os itens */
    width: 100%;
    max-width: 1200px; /* Limita a largura para melhor controle */
}

.Menu li {
    padding: 0 20px; /* Aumenta o espaçamento horizontal */
    display: inline-block !important; /* Força a exibição inline-block */
    text-align: center;
    margin: 0 5px; /* Adiciona margem entre os itens */
    visibility: visible !important; /* Força a visibilidade */
    opacity: 1 !important; /* Força a opacidade */
}



/* Garantir que todos os itens do menu sejam exibidos */
.Menu li {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.Menu li.logo-item {
    margin-right: 20px;
}

.Menu .logotipo {
    max-height: 40px;
    width: auto;
}

.Menu a {
    color: white;
    text-decoration: none;
    display: block;
    font-weight: 500;
    transition: all 0.3s;
    padding: 10px 0;
    text-align: center;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.Menu a:hover {
    color: #ddd;
}

.Menu a.active {
    color: #fff;
    border-bottom: 2px solid white;
}

.welcome-text {
    color: #fff;
    font-size: 0.9em;
    white-space: nowrap;
    margin-left: auto; /* Empurra para a direita */
}

/* Estilos para o conteúdo principal */
.wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.content {
    flex: 1;
    padding: 20px; /* Reduzido o padding-top que compensava o menu fixo */
}

/* Estilos para o Dashboard */
.dashboard {
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 10px;
    margin-bottom: 30px;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card i {
    font-size: 2em;
    color: #4C4C4C;
    margin-bottom: 10px;
}

.stat-card .number {
    font-size: 24px;
    font-weight: bold;
    color: #4C4C4C;
    margin: 10px 0;
}

.stat-card .description {
    color: #666;
    font-size: 14px;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.progress-container {
    margin-top: 15px;
}

.progress-item {
    margin-bottom: 15px;
}

/* Estilos para barras de progresso removidos para evitar conflitos com Bootstrap */

/* Estilos para os Cards de Gestão */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.btn {
    background: #4C4C4C;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
    transition: background 0.3s ease;
}

.btn:hover {
    background: #333;
}

.status-label {
    font-size: 0.8em;
    color: #666;
    margin-left: 10px;
}

/* Estilos para a tabela de atividades */
.activity-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.activity-table th,
.activity-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.activity-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

.status.completed {
    background-color: #e0f2e9;
    color: #4C4C4C;
}

.status.in-progress {
    background-color: #fff3cd;
    color: #4C4C4C;
}

.status.pending {
    background-color: #f8d7da;
    color: #4C4C4C;
}

/* Estilos do login */
.login-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.login-form {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    width: 90%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    font-size: 22px;
    margin-bottom: 20px;
}

/* Estilos do banner */
.header2 {
    width: 100%;
    padding: 40px 20px;
    background-color: #f5f5f5;
    text-align: center;
}

.banner-text {
    max-width: 800px;
    margin: 0 auto;
}

.banner-text h1 {
    color: #333;
    margin-bottom: 20px;
}

.banner-text p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
}

.banner-text img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Estilos do formulário */
.formulario {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    width: 90%;
    max-width: 400px;
    margin: 20px auto;
}

.cabecalho_formulario {
    text-align: center;
    font-size: 22px;
    margin-bottom: 20px;
}

.mb-3 {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-control:focus {
    border-color: #4C4C4C;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 76, 76, 0.2);
}

/* Estilos responsivos */
@media screen and (max-width: 768px) {
    .banner-text {
        padding: 20px;
    }

    .banner-text h1 {
        font-size: 24px;
    }

    .banner-text p {
        font-size: 16px;
    }

    .formulario {
        width: 95%;
    }
}

/* Estilos para mensagens de erro/sucesso */
.alert {
    padding: 15px;
    margin: 20px 0;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Estilos para tabelas */
.table-responsive {
    overflow-x: auto;
    margin: 20px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.table th,
.table td {
    padding: 12px 15px;
    border: 1px solid #ddd;
    text-align: left;
    vertical-align: middle;
}

.table th {
    background-color: #4C4C4C;
    color: white;
    font-weight: 600;
    border: none;
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.table tr:hover {
    background-color: #f5f5f5;
}

/* Estilos para células de ação */
.action-cell {
    white-space: nowrap;
    text-align: center;
}

/* Estilos para botões de ação */
.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn-edit,
.btn-delete,
.btn-view,
.btn-add,
.btn {
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    font-weight: 500;
    margin: 0 3px;
    font-family: 'Poppins', sans-serif;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.btn-view, .btn-info {
    background-color: #4C4C4C !important;
    color: white !important;
    border-color: #4C4C4C !important;
}

.btn-view:hover, .btn-info:hover {
    background-color: #3a3a3a !important;
    border-color: #3a3a3a !important;
}

.btn-edit, .btn-warning {
    background-color: #FFA500 !important;
    color: white !important;
    border-color: #FFA500 !important;
}

.btn-edit:hover, .btn-warning:hover {
    background-color: #FF8C00 !important;
    border-color: #FF8C00 !important;
}

.btn-delete, .btn-danger {
    background-color: #DC3545 !important;
    color: white !important;
    border-color: #DC3545 !important;
}

.btn-delete:hover, .btn-danger:hover {
    background-color: #C82333 !important;
    border-color: #C82333 !important;
}

.btn-add, .btn-success {
    background-color: #28A745 !important;
    color: white !important;
    border-color: #28A745 !important;
}

.btn-add:hover, .btn-success:hover {
    background-color: #218838 !important;
    border-color: #218838 !important;
}

/* Estilos para badges de status */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
}

.bg-success {
    background-color: #28A745 !important;
    color: white !important;
}

.bg-warning {
    background-color: #FFC107 !important;
    color: #212529 !important;
}

.bg-danger {
    background-color: #DC3545 !important;
    color: white !important;
}

.bg-info {
    background-color: #17A2B8 !important;
    color: white !important;
}

.bg-secondary {
    background-color: #6C757D !important;
    color: white !important;
}

/* Estilos para botões pequenos */
.btn-sm {
    padding: 4px 8px;
    font-size: 0.875rem;
}

/* Estilos para botões grandes */
.btn-lg {
    padding: 8px 16px;
    font-size: 1.25rem;
}

/* Estilos para tabelas responsivas em dispositivos móveis */
@media (max-width: 768px) {
    .action-cell {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .action-cell .btn {
        width: 100%;
        margin: 2px 0;
    }
}

/* Ajustes para a seção hero da página inicial - Texto e imagem lado a lado centralizados */
.hero-section {
    width: 100%;
    max-width: 1200px; /* Limita a largura máxima para melhor controle */
    margin: 0 auto;
    padding: 3rem 1rem;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: row; /* Elementos lado a lado */
    align-items: center; /* Centraliza verticalmente */
    justify-content: center; /* Centraliza horizontalmente */
    box-sizing: border-box;
    overflow: hidden;
    text-align: center;
}

.hero-content {
    flex: 1;
    min-width: 300px;
    max-width: 500px;
    padding: 0 20px;
    text-align: center; /* Centraliza o texto */
}

.hero-content h1,
.hero-content h2,
.hero-content p {
    text-align: center; /* Garante que todo texto esteja centralizado */
}

.hero-image {
    flex: 1;
    min-width: 300px;
    max-width: 500px;
    display: flex;
    justify-content: center; /* Centraliza horizontalmente */
    align-items: center; /* Centraliza verticalmente */
    padding: 0 20px;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    display: block;
}

/* Ajustes responsivos para dispositivos móveis */
@media (max-width: 992px) {
    .hero-section {
        flex-direction: column; /* Em telas menores, empilha os elementos */
    }
    
    .hero-content {
        margin-bottom: 2rem;
    }
}

/* Garantir que o container principal tenha largura consistente */
.container, 
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    box-sizing: border-box;
}

/* Ajuste para garantir que o wrapper mantenha a largura correta */
.wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}









