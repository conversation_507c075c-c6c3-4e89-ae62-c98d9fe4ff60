# 📦 GUIA DE BACKUP MANUAL COMPLETO

## 🚨 IMPORTANTE: FAÇA BACKUP ANTES DAS CORREÇÕES

Este guia te ajuda a criar um backup completo da aplicação antes de aplicar as correções críticas.

## 📋 CHECKLIST DE BACKUP

### ✅ **1. BACKUP DOS ARQUIVOS DA APLICAÇÃO**

#### **Método 1: Cópia Manual (Windows)**
```
1. Abra o Windows Explorer
2. Navegue até: C:\Users\<USER>\OneDrive\Desktop\Projeto final
3. Clique com botão direito na pasta "PHP PROJETO"
4. <PERSON><PERSON><PERSON><PERSON> "Copiar"
5. Crie uma nova pasta: "backup_aplicacao_[DATA]"
6. <PERSON> a pasta "PHP PROJETO" dentro do backup
7. Copie também todos os arquivos .ps1 e .md da raiz
```

#### **Método 2: Via Linha de Comando**
```cmd
# Abrir Prompt de Comando como Administrador
cd "C:\Users\<USER>\OneDrive\Desktop\Projeto final"

# Criar pasta de backup
mkdir backup_completo_%date:~-4,4%%date:~-10,2%%date:~-7,2%

# Copiar arquivos
xcopy "PHP PROJETO" "backup_completo_%date:~-4,4%%date:~-10,2%%date:~-7,2%\PHP PROJETO" /E /I /H
copy *.ps1 "backup_completo_%date:~-4,4%%date:~-10,2%%date:~-7,2%\"
copy *.md "backup_completo_%date:~-4,4%%date:~-10,2%%date:~-7,2%\"
```

### ✅ **2. BACKUP DO BANCO DE DADOS**

#### **Método 1: Via phpMyAdmin (Recomendado)**
```
1. Abra o navegador
2. Acesse: http://localhost/phpmyadmin
3. Faça login (usuário: root, senha: IEFP2025*)
4. Clique no banco "controle_obras" (lado esquerdo)
5. Clique na aba "Exportar" (topo)
6. Selecione:
   ✓ Método: Personalizado
   ✓ Tabelas: Selecionar todas
   ✓ Formato: SQL
   ✓ Estrutura: ✓ Adicionar declaração CREATE TABLE
   ✓ Dados: ✓ Adicionar declaração INSERT
7. Clique em "Executar"
8. Salve como: backup_controle_obras_[DATA].sql
```

#### **Método 2: Via Linha de Comando (se MySQL estiver no PATH)**
```cmd
# Navegar até a pasta de backup
cd backup_completo_[DATA]

# Fazer backup do banco
mysqldump -u root -p controle_obras > backup_controle_obras.sql
# Digite a senha quando solicitado: IEFP2025*
```

#### **Método 3: Via MySQL Workbench**
```
1. Abra MySQL Workbench
2. Conecte ao servidor local
3. Clique em "Data Export" (lado esquerdo)
4. Selecione o schema "controle_obras"
5. Marque todas as tabelas
6. Escolha "Export to Self-Contained File"
7. Defina o caminho: backup_controle_obras_[DATA].sql
8. Clique em "Start Export"
```

### ✅ **3. VERIFICAR BACKUP CRIADO**

#### **Estrutura esperada do backup:**
```
backup_completo_[DATA]/
├── PHP PROJETO/
│   ├── php/
│   ├── SQL/
│   ├── css/
│   └── ...
├── backup_controle_obras.sql
├── criar_backup_completo.ps1
├── README_BACKUP.md
└── outros arquivos...
```

#### **Verificações importantes:**
- [ ] Pasta "PHP PROJETO" copiada completamente
- [ ] Arquivo .sql do banco de dados criado
- [ ] Tamanho do arquivo .sql > 0 KB
- [ ] Scripts de backup incluídos
- [ ] Data/hora do backup anotada

### ✅ **4. TESTAR BACKUP (OPCIONAL MAS RECOMENDADO)**

#### **Teste rápido do backup do banco:**
```sql
-- Abra o arquivo .sql em um editor de texto
-- Verifique se contém:
CREATE TABLE `obras` (
CREATE TABLE `utilizadores` (
CREATE TABLE `registro_horas` (
INSERT INTO `obras` VALUES
INSERT INTO `utilizadores` VALUES
```

## 🔧 SCRIPTS AUTOMÁTICOS DISPONÍVEIS

### **PowerShell (Windows)**
```powershell
# Execute no PowerShell como Administrador
.\criar_backup_completo.ps1

# Ou com opções:
.\criar_backup_completo.ps1 -BackupPath "meu_backup" -CompressBackup
```

### **Parâmetros disponíveis:**
- `-BackupPath`: Nome da pasta de backup
- `-IncludeDatabase`: Incluir backup do banco (padrão: true)
- `-CompressBackup`: Comprimir em ZIP (padrão: true)

## 📊 TAMANHOS ESPERADOS

### **Arquivos da aplicação:**
- PHP PROJETO/: ~5-20 MB
- Scripts auxiliares: ~1 MB

### **Banco de dados:**
- Backup .sql: ~100 KB - 10 MB (dependendo dos dados)
- Se vazio: ~50 KB (apenas estrutura)

## 🚨 PROBLEMAS COMUNS E SOLUÇÕES

### **Erro: "Acesso negado"**
```
Solução: Execute como Administrador
- Clique direito no PowerShell/CMD
- Selecione "Executar como administrador"
```

### **Erro: "mysqldump não encontrado"**
```
Solução: Use phpMyAdmin ou adicione MySQL ao PATH
- Método mais fácil: Use phpMyAdmin
- Ou localize mysqldump.exe e use caminho completo
```

### **Erro: "Banco não encontrado"**
```
Solução: Verificar nome do banco
- Acesse phpMyAdmin
- Verifique se o banco se chama "controle_obras"
- Se diferente, ajuste o nome no comando
```

### **Backup muito pequeno**
```
Verificações:
- Arquivo .sql < 10 KB = provavelmente só estrutura
- Verifique se há dados nas tabelas
- Confirme se todas as tabelas foram exportadas
```

## ✅ APÓS CRIAR O BACKUP

### **1. Validar backup:**
- [ ] Arquivos copiados corretamente
- [ ] Banco de dados exportado
- [ ] Tamanhos condizentes
- [ ] Data/hora anotada

### **2. Próximos passos:**
1. **Aplicar correções críticas**
   ```sql
   -- Execute no phpMyAdmin:
   PHP PROJETO/SQL/EXECUTAR_CORRECOES_CRITICAS.sql
   ```

2. **Testar aplicação**
   - Login/logout
   - Criar obra
   - Registrar horas
   - Criar orçamento

3. **Manter backup**
   - Não delete o backup
   - Mantenha por pelo menos 1 semana
   - Use para restaurar se necessário

## 🔄 COMO RESTAURAR (SE NECESSÁRIO)

### **Restaurar arquivos:**
```cmd
# Copiar de volta
xcopy "backup_completo_[DATA]\PHP PROJETO" "PHP PROJETO" /E /I /H /Y
```

### **Restaurar banco:**
```sql
-- Via phpMyAdmin:
1. Acesse phpMyAdmin
2. Selecione banco "controle_obras"
3. Aba "Importar"
4. Escolha arquivo backup_controle_obras.sql
5. Clique "Executar"
```

---

## 🎯 RESUMO RÁPIDO

1. **Copie a pasta "PHP PROJETO"** para local seguro
2. **Exporte o banco** via phpMyAdmin
3. **Anote data/hora** do backup
4. **Teste se backup está completo**
5. **Aplique as correções** com segurança

**⚠️ NÃO PULE O BACKUP!** É sua garantia de segurança.
