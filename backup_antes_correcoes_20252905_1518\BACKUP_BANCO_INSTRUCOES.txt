===============================================
INSTRUÇÕES PARA BACKUP DO BANCO DE DADOS
===============================================

IMPORTANTE: Execute este backup ANTES de aplicar as correções críticas!

MÉTODO 1: Via phpMyAdmin (RECOMENDADO)
===============================================
1. Abra o navegador
2. Acesse: http://localhost/phpmyadmin
3. Faça login:
   - Usuário: root
   - Senha: IEFP2025*
4. Clique no banco "controle_obras" (lado esquerdo)
5. Clique na aba "Exportar" (topo da página)
6. Configure as opções:
   ✓ Método: Personalizado
   ✓ Tabelas: Selecionar todas
   ✓ Formato: SQL
   ✓ Estrutura: ✓ Adicionar declaração CREATE TABLE
   ✓ Dados: ✓ Adicionar declaração INSERT
7. Clique em "Executar"
8. Salve o arquivo como: backup_controle_obras_20250129.sql
9. Mova o arquivo para esta pasta de backup

MÉTODO 2: Via Linha de Comando
===============================================
1. Abra o Prompt de Comando
2. Execute:
   mysqldump -u root -p controle_obras > backup_controle_obras_20250129.sql
3. Digite a senha quando solicitado: IEFP2025*
4. Mova o arquivo para esta pasta de backup

VERIFICAÇÃO DO BACKUP
===============================================
- O arquivo .sql deve ter pelo menos 50 KB
- Abra o arquivo em um editor de texto
- Verifique se contém:
  * CREATE TABLE `obras`
  * CREATE TABLE `utilizadores`
  * CREATE TABLE `registro_horas`
  * INSERT INTO statements (se houver dados)

ESTRUTURA ESPERADA DO BANCO
===============================================
Tabelas principais:
- obras (projetos de construção)
- utilizadores (usuários do sistema)
- registro_horas (registro de horas trabalhadas)
- orcamentos (orçamentos dos projetos)
- materiais (materiais dos projetos)

APÓS FAZER O BACKUP
===============================================
1. Verifique se o arquivo .sql foi criado
2. Confirme que tem tamanho > 0 KB
3. Aplique as correções críticas:
   - Execute: PHP PROJETO/SQL/EXECUTAR_CORRECOES_CRITICAS.sql
4. Teste a aplicação após as correções

COMO RESTAURAR (SE NECESSÁRIO)
===============================================
Via phpMyAdmin:
1. Acesse phpMyAdmin
2. Selecione o banco "controle_obras"
3. Clique em "Importar"
4. Escolha o arquivo backup_controle_obras_20250129.sql
5. Clique em "Executar"

Via linha de comando:
mysql -u root -p controle_obras < backup_controle_obras_20250129.sql

===============================================
BACKUP CRIADO EM: 29/01/2025
ANTES DAS CORREÇÕES CRÍTICAS
===============================================
