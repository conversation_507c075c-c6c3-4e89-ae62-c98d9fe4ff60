<?php
require_once 'conexao.php';

echo "<h1>Verificação de Usuários no Sistema</h1>";

// Conectar ao banco de dados
$conn = connectToDatabase();
if (!$conn) {
    echo "<p style='color: red;'>❌ Erro de conexão com o banco de dados: " . mysqli_connect_error() . "</p>";
    exit;
}

// Verificar se a tabela utilizadores existe
$result = mysqli_query($conn, "SHOW TABLES LIKE 'utilizadores'");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p style='color: green;'>✅ Tabela 'utilizadores' existe</p>";
    
    // Contar usuários
    $count_users = mysqli_query($conn, "SELECT COUNT(*) as total FROM utilizadores");
    $total_users = mysqli_fetch_assoc($count_users)['total'];
    echo "<p>📊 Total de usuários cadastrados: $total_users</p>";
    
    if ($total_users > 0) {
        // Listar usuários (sem mostrar senhas)
        $users_query = mysqli_query($conn, "SELECT id_utilizadores, nome_utilizador, cargo_utilizador FROM utilizadores ORDER BY id_utilizadores");
        
        echo "<h3>Usuários cadastrados:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Cargo</th><th>Descrição do Cargo</th></tr>";
        
        while ($user = mysqli_fetch_assoc($users_query)) {
            $cargo_desc = '';
            switch($user['cargo_utilizador']) {
                case '1': $cargo_desc = 'Administrador'; break;
                case '2': $cargo_desc = 'Gerente'; break;
                case '3': $cargo_desc = 'Supervisor'; break;
                case '4': $cargo_desc = 'Funcionário'; break;
                default: $cargo_desc = 'Desconhecido';
            }
            
            echo "<tr>";
            echo "<td>" . $user['id_utilizadores'] . "</td>";
            echo "<td>" . htmlspecialchars($user['nome_utilizador']) . "</td>";
            echo "<td>" . $user['cargo_utilizador'] . "</td>";
            echo "<td>" . $cargo_desc . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Instruções para Login:</h3>";
        echo "<ol>";
        echo "<li>Vá para a página principal: <a href='Projeto.php'>Projeto.php</a></li>";
        echo "<li>Use um dos usuários listados acima</li>";
        echo "<li>Para criar orçamentos, você precisa ser Administrador (cargo 1) ou Gerente (cargo 2)</li>";
        echo "</ol>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ Nenhum usuário cadastrado no sistema!</p>";
        echo "<p>Você precisa criar um usuário primeiro. Vá para: <a href='Registar_utilizador.php'>Registar_utilizador.php</a></p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Tabela 'utilizadores' não existe</p>";
    echo "<p>O sistema precisa ser configurado primeiro.</p>";
}

// Fechar conexão
mysqli_close($conn);
?>
