# Script PowerShell para criar backup dos ficheiros essenciais antes da limpeza
# Garante que pode restaurar se algo correr mal

Write-Host "=== SCRIPT DE BACKUP ANTES DA LIMPEZA ===" -ForegroundColor Green
Write-Host "Este script cria um backup dos ficheiros essenciais antes da limpeza." -ForegroundColor Yellow
Write-Host ""

# Criar diretório de backup com timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "backup_antes_limpeza_$timestamp"

Write-Host "Criando diretório de backup: $backupDir" -ForegroundColor Cyan
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# Função para copiar ficheiro se existir
function Backup-File {
    param([string]$SourcePath, [string]$DestinationPath)
    
    if (Test-Path $SourcePath) {
        $destDir = Split-Path $DestinationPath -Parent
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item $SourcePath $DestinationPath -Force
        Write-Host "✓ Backup: $SourcePath" -ForegroundColor Green
    } else {
        Write-Host "- Não encontrado: $SourcePath" -ForegroundColor Gray
    }
}

# Função para copiar diretório se existir
function Backup-Directory {
    param([string]$SourcePath, [string]$DestinationPath)
    
    if (Test-Path $SourcePath) {
        Copy-Item $SourcePath $DestinationPath -Recurse -Force
        Write-Host "✓ Backup diretório: $SourcePath" -ForegroundColor Green
    } else {
        Write-Host "- Diretório não encontrado: $SourcePath" -ForegroundColor Gray
    }
}

Write-Host "Fazendo backup dos ficheiros essenciais..." -ForegroundColor Cyan

# Backup das páginas principais
Backup-File "PHP PROJETO\php\Projeto.php" "$backupDir\PHP PROJETO\php\Projeto.php"
Backup-File "PHP PROJETO\php\Projeto pag 2.php" "$backupDir\PHP PROJETO\php\Projeto pag 2.php"
Backup-File "PHP PROJETO\php\Projeto pag 3.php" "$backupDir\PHP PROJETO\php\Projeto pag 3.php"
Backup-File "PHP PROJETO\php\Projeto pag 4.php" "$backupDir\PHP PROJETO\php\Projeto pag 4.php"
Backup-File "PHP PROJETO\php\Projeto pag 5.php" "$backupDir\PHP PROJETO\php\Projeto pag 5.php"

# Backup dos ficheiros de configuração essenciais
Backup-File "PHP PROJETO\php\conexao.php" "$backupDir\PHP PROJETO\php\conexao.php"
Backup-File "PHP PROJETO\php\config.php" "$backupDir\PHP PROJETO\php\config.php"
Backup-File "PHP PROJETO\php\verificar_permissao.php" "$backupDir\PHP PROJETO\php\verificar_permissao.php"
Backup-File "PHP PROJETO\php\security_functions.php" "$backupDir\PHP PROJETO\php\security_functions.php"

# Backup dos ficheiros de processamento
Backup-File "PHP PROJETO\php\processar_obra.php" "$backupDir\PHP PROJETO\php\processar_obra.php"
Backup-File "PHP PROJETO\php\processar_material.php" "$backupDir\PHP PROJETO\php\processar_material.php"
Backup-File "PHP PROJETO\php\processar_orcamento.php" "$backupDir\PHP PROJETO\php\processar_orcamento.php"
Backup-File "PHP PROJETO\php\processar_horas.php" "$backupDir\PHP PROJETO\php\processar_horas.php"
Backup-File "PHP PROJETO\php\registrar_horas.php" "$backupDir\PHP PROJETO\php\registrar_horas.php"
Backup-File "PHP PROJETO\php\Criar_utilizador.php" "$backupDir\PHP PROJETO\php\Criar_utilizador.php"

# Backup dos ficheiros de interface
Backup-File "PHP PROJETO\php\Cabeçalho.php" "$backupDir\PHP PROJETO\php\Cabeçalho.php"
Backup-File "PHP PROJETO\php\rodape.php" "$backupDir\PHP PROJETO\php\rodape.php"
Backup-File "PHP PROJETO\php\menu.php" "$backupDir\PHP PROJETO\php\menu.php"
Backup-File "PHP PROJETO\php\nav.php" "$backupDir\PHP PROJETO\php\nav.php"
Backup-File "PHP PROJETO\php\navbar.php" "$backupDir\PHP PROJETO\php\navbar.php"

# Backup dos ficheiros CSS essenciais
Backup-File "PHP PROJETO\php\projeto.css" "$backupDir\PHP PROJETO\php\projeto.css"
Backup-File "PHP PROJETO\php\homepage.css" "$backupDir\PHP PROJETO\php\homepage.css"
Backup-File "PHP PROJETO\php\menu_style.css" "$backupDir\PHP PROJETO\php\menu_style.css"
Backup-File "PHP PROJETO\php\navbar.css" "$backupDir\PHP PROJETO\php\navbar.css"
Backup-File "PHP PROJETO\CSS\projeto.css" "$backupDir\PHP PROJETO\CSS\projeto.css"

# Backup dos ficheiros JavaScript essenciais (que devem ser mantidos)
Backup-File "PHP PROJETO\php\fix_admin_menu.js" "$backupDir\PHP PROJETO\php\fix_admin_menu.js"
Backup-File "PHP PROJETO\php\csrf_fix.js" "$backupDir\PHP PROJETO\php\csrf_fix.js"
Backup-File "PHP PROJETO\php\csrf_init.js" "$backupDir\PHP PROJETO\php\csrf_init.js"

# Backup das imagens utilizadas
Backup-File "PHP PROJETO\php\Imagem1.png" "$backupDir\PHP PROJETO\php\Imagem1.png"

# Backup dos ficheiros de edição e exclusão
Backup-File "PHP PROJETO\php\editar_obra.php" "$backupDir\PHP PROJETO\php\editar_obra.php"
Backup-File "PHP PROJETO\php\editar_material.php" "$backupDir\PHP PROJETO\php\editar_material.php"
Backup-File "PHP PROJETO\php\editar_horas.php" "$backupDir\PHP PROJETO\php\editar_horas.php"
Backup-File "PHP PROJETO\php\excluir_material.php" "$backupDir\PHP PROJETO\php\excluir_material.php"
Backup-File "PHP PROJETO\php\excluir_horas.php" "$backupDir\PHP PROJETO\php\excluir_horas.php"
Backup-File "PHP PROJETO\php\eliminar_utilizador.php" "$backupDir\PHP PROJETO\php\eliminar_utilizador.php"

# Backup de outros ficheiros importantes
Backup-File "PHP PROJETO\php\get_csrf_token.php" "$backupDir\PHP PROJETO\php\get_csrf_token.php"
Backup-File "PHP PROJETO\php\get_registro.php" "$backupDir\PHP PROJETO\php\get_registro.php"
Backup-File "PHP PROJETO\php\obter_dados_obra.php" "$backupDir\PHP PROJETO\php\obter_dados_obra.php"
Backup-File "PHP PROJETO\php\listar_obras.php" "$backupDir\PHP PROJETO\php\listar_obras.php"
Backup-File "PHP PROJETO\php\listar_utilizadores.php" "$backupDir\PHP PROJETO\php\listar_utilizadores.php"
Backup-File "PHP PROJETO\php\detalhes_obra.php" "$backupDir\PHP PROJETO\php\detalhes_obra.php"
Backup-File "PHP PROJETO\php\detalhes_orcamento.php" "$backupDir\PHP PROJETO\php\detalhes_orcamento.php"

# Backup do diretório SQL (scripts da base de dados)
Backup-Directory "PHP PROJETO\SQL" "$backupDir\PHP PROJETO\SQL"

Write-Host ""
Write-Host "=== BACKUP CONCLUÍDO ===" -ForegroundColor Green
Write-Host "Diretório de backup criado: $backupDir" -ForegroundColor Yellow
Write-Host ""
Write-Host "✅ Todos os ficheiros essenciais foram salvos em backup." -ForegroundColor Green
Write-Host "✅ Agora pode executar o script de limpeza com segurança." -ForegroundColor Green
Write-Host ""
Write-Host "Para restaurar em caso de problemas:" -ForegroundColor Yellow
Write-Host "1. Copie os ficheiros de volta do diretório $backupDir" -ForegroundColor Yellow
Write-Host "2. Ou execute: Copy-Item '$backupDir\*' . -Recurse -Force" -ForegroundColor Yellow
Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
