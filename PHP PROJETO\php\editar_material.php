<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verificar token CSRF - modificação aqui para resolver o problema
    if (!isset($_POST['csrf_token'])) {
        // Se o token não estiver presente, gerar um novo e continuar
        error_log("Token CSRF não encontrado no formulário de edição de material");
        $_SESSION['csrf_token'] = generateCSRFToken();
    } else if (!verifyCSRFToken($_POST['csrf_token'])) {
        // Se o token estiver presente mas for inválido, regenerar e continuar
        error_log("Token CSRF inválido: " . $_POST['csrf_token']);
        $_SESSION['csrf_token'] = generateCSRFToken();
    }
    
    // Continuar com o processamento normal
    $conn = connectToDatabase();
    // Preparar e limpar os dados
    $material_id = mysqli_real_escape_string($conn, $_POST['material_id']);
    $nome_material = mysqli_real_escape_string($conn, $_POST['nome_material_edit']);
    $quantidade = mysqli_real_escape_string($conn, $_POST['quantidade_edit']);
    $obra_id = mysqli_real_escape_string($conn, $_POST['obra_id_material_edit']);
    $unidade_medida = mysqli_real_escape_string($conn, $_POST['unidade_medida_edit']);

    // Validar dados
    $erros = [];

    if (empty($material_id)) {
        $erros[] = "ID do material não fornecido";
    }

    if (empty($nome_material)) {
        $erros[] = "O nome do material é obrigatório";
    }

    $quantidade_validada = validateNumber($quantidade, 0.01);
    if ($quantidade_validada === false) {
        $erros[] = "A quantidade deve ser um número positivo";
    } else {
        $quantidade = $quantidade_validada;
    }

    if (empty($obra_id)) {
        $erros[] = "É necessário selecionar uma obra";
    }

    // Se não houver erros, atualizar no banco de dados
    if (empty($erros)) {
        // Verificar se a coluna unidade_medida existe
        $query = "SHOW COLUMNS FROM materiais LIKE 'unidade_medida'";
        $result = mysqli_query($conn, $query);
        $unidade_medida_exists = mysqli_num_rows($result) > 0;

        if ($unidade_medida_exists) {
            $query = "UPDATE materiais SET nome = ?, quantidade = ?, obras_id = ?, unidade_medida = ? WHERE material_id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "sissi", $nome_material, $quantidade, $obra_id, $unidade_medida, $material_id);
        } else {
            $query = "UPDATE materiais SET nome = ?, quantidade = ?, obras_id = ? WHERE material_id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "siii", $nome_material, $quantidade, $obra_id, $material_id);
        }

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['mensagem'] = "Material atualizado com sucesso!";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao atualizar material: " . mysqli_error($conn);
            $_SESSION['tipo_mensagem'] = "danger";
        }

        mysqli_stmt_close($stmt);
    } else {
        $_SESSION['mensagem'] = "Erro: " . implode(", ", $erros);
        $_SESSION['tipo_mensagem'] = "danger";
    }

    mysqli_close($conn);

    // Redirecionar explicitamente para a página de obras
    header("Location: Projeto pag 2.php");
    exit();
}
?>



