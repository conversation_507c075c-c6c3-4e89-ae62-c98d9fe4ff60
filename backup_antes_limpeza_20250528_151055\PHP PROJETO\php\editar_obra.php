<?php
session_start();
// Incluir arquivos necessários
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Verificar se o formulário foi enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug para verificar os dados recebidos
    error_log("Dados recebidos em editar_obra.php: " . print_r($_POST, true));

    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['mensagem'] = "Erro de segurança. Por favor, tente novamente.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }
    // Inicializar variável de erro
    $erro = false;
    $mensagem_erro = "";

    // Obter dados do formulário
    $id_obra = mysqli_real_escape_string($conn, $_POST['id_obra']);
    $nome_obra = mysqli_real_escape_string($conn, $_POST['nome_obra']);
    $localizacao = mysqli_real_escape_string($conn, $_POST['localizacao']);
    $data_inicio_raw = mysqli_real_escape_string($conn, $_POST['data_inicio']);
    $status = isset($_POST['status']) ? mysqli_real_escape_string($conn, $_POST['status']) : 'Em andamento';

    // Validar data de início usando a função de validação
    $data_inicio = validateDate($data_inicio_raw);
    if ($data_inicio === false) {
        $erro = true;
        $mensagem_erro = "Data de início inválida. Use uma data válida no formato AAAA-MM-DD.";
    }

    // Validar data de fim (se fornecida) usando a função de validação
    $data_fim = null;
    if (isset($_POST['data_fim']) && !empty($_POST['data_fim'])) {
        $data_fim_raw = mysqli_real_escape_string($conn, $_POST['data_fim']);
        $data_fim = validateDate($data_fim_raw);

        if ($data_fim === false) {
            $erro = true;
            $mensagem_erro = "Data de fim inválida. Use uma data válida no formato AAAA-MM-DD.";
        }
    }

    // Se houver erro, armazenar mensagem na sessão e redirecionar
    if ($erro) {
        $_SESSION['mensagem'] = $mensagem_erro;
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }

    // Verificar se a obra existe usando prepared statement
    $query_check_obra = "SELECT obras_id FROM obras WHERE obras_id = ?";
    $stmt_check = mysqli_prepare($conn, $query_check_obra);
    mysqli_stmt_bind_param($stmt_check, "i", $id_obra);
    mysqli_stmt_execute($stmt_check);
    mysqli_stmt_store_result($stmt_check);

    if (mysqli_stmt_num_rows($stmt_check) == 0) {
        mysqli_stmt_close($stmt_check);
        mysqli_close($conn);

        $_SESSION['mensagem'] = "Erro: A obra selecionada não existe.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }
    mysqli_stmt_close($stmt_check);

    // Verificar a estrutura da tabela obras
    $query_describe = "DESCRIBE obras";
    $result_describe = mysqli_query($conn, $query_describe);
    $colunas = [];

    while ($row = mysqli_fetch_assoc($result_describe)) {
        $colunas[] = $row['Field'];
        error_log("Coluna encontrada: " . $row['Field']);
    }

    // Determinar o nome correto da coluna de endereço
    $coluna_endereco = in_array('endereço', $colunas) ? 'endereço' : 
                      (in_array('endereco', $colunas) ? 'endereco' : 
                      (in_array('localizacao', $colunas) ? 'localizacao' : 'endereço'));

    error_log("Coluna de endereço a ser usada: " . $coluna_endereco);

    // Atualizar obra usando prepared statement com o nome correto da coluna
    $query_update = "UPDATE obras SET nome_obra = ?, $coluna_endereco = ?, data_inicio = ?, status = ?";

    // Adicionar data_fim à query se estiver definida
    if ($data_fim !== null) {
        $query_update .= ", data_fim = ?";
    }

    $query_update .= " WHERE obras_id = ?";

    // Adicionar log para depuração
    error_log("Query de atualização: " . $query_update);
    error_log("ID da obra: " . $id_obra);
    error_log("Nome da obra: " . $nome_obra);
    error_log("Localização: " . $localizacao);
    error_log("Data de início: " . $data_inicio);
    error_log("Data de fim: " . ($data_fim ?? 'NULL'));
    error_log("Status: " . $status);

    $stmt_update = mysqli_prepare($conn, $query_update);

    if (!$stmt_update) {
        error_log("Erro ao preparar a query: " . mysqli_error($conn));
        $_SESSION['mensagem'] = "Erro ao preparar a query: " . mysqli_error($conn);
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }

    if ($data_fim !== null) {
        mysqli_stmt_bind_param($stmt_update, "sssssi", $nome_obra, $localizacao, $data_inicio, $status, $data_fim, $id_obra);
    } else {
        mysqli_stmt_bind_param($stmt_update, "ssssi", $nome_obra, $localizacao, $data_inicio, $status, $id_obra);
    }

    // Verificar se o bind_param foi bem-sucedido
    if (mysqli_stmt_errno($stmt_update)) {
        error_log("Erro ao vincular parâmetros: " . mysqli_stmt_error($stmt_update));
        $_SESSION['mensagem'] = "Erro ao vincular parâmetros: " . mysqli_stmt_error($stmt_update);
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }

    if (mysqli_stmt_execute($stmt_update)) {
        mysqli_stmt_close($stmt_update);

        // Armazenar mensagem de sucesso na sessão
        $_SESSION['mensagem'] = "Obra atualizada com sucesso!";
        $_SESSION['tipo_mensagem'] = "success";
        header("Location: Projeto pag 2.php");
        exit();
    } else {
        $erro_msg = mysqli_stmt_error($stmt_update);
        mysqli_stmt_close($stmt_update);

        // Armazenar mensagem de erro na sessão
        $_SESSION['mensagem'] = "Erro ao atualizar obra: " . $erro_msg;
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }
} else {
    // Se o formulário não foi enviado, redirecionar para a página de obras
    header("Location: Projeto pag 2.php");
    exit();
}

// Fechar conexão
mysqli_close($conn);
?>









