<?php
// Script para verificar e corrigir a tabela de registro de horas
require_once 'conexao.php';

function verificarECorrigirRegistroHoras() {
    $conn = connectToDatabase();
    $mensagens = [];
    
    // 1. Verificar se a tabela existe
    $check_table = mysqli_query($conn, "SHOW TABLES LIKE 'registro_horas'");
    if (mysqli_num_rows($check_table) == 0) {
        $mensagens[] = "A tabela 'registro_horas' não existe. Criando...";
        
        // Criar a tabela
        $create_table = "CREATE TABLE IF NOT EXISTS `registro_horas` (
          `id` INT NOT NULL AUTO_INCREMENT,
          `id_obra` INT NOT NULL,
          `id_usuario` INT NOT NULL,
          `horas` DECIMAL(5, 2) NOT NULL,
          `data_registro` DATE NOT NULL,
          `descricao` TEXT NULL,
          `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          CONSTRAINT `fk_registro_horas_obras`
            FOREIGN KEY (`id_obra`)
            REFERENCES `obras` (`obras_id`)
            ON DELETE CASCADE
            ON UPDATE CASCADE,
          CONSTRAINT `fk_registro_horas_usuarios`
            FOREIGN KEY (`id_usuario`)
            REFERENCES `utilizadores` (`id_utilizadores`)
            ON DELETE CASCADE
            ON UPDATE CASCADE
        )";
        
        if (mysqli_query($conn, $create_table)) {
            $mensagens[] = "Tabela 'registro_horas' criada com sucesso.";
        } else {
            $mensagens[] = "Erro ao criar tabela: " . mysqli_error($conn);
        }
    } else {
        $mensagens[] = "A tabela 'registro_horas' já existe.";
        
        // 2. Verificar estrutura da tabela
        $check_columns = mysqli_query($conn, "DESCRIBE registro_horas");
        $colunas = [];
        while ($col = mysqli_fetch_assoc($check_columns)) {
            $colunas[] = $col['Field'];
        }
        
        $mensagens[] = "Colunas encontradas: " . implode(", ", $colunas);
        
        // 3. Verificar se há dados
        $check_data = mysqli_query($conn, "SELECT COUNT(*) as count FROM registro_horas");
        $count = mysqli_fetch_assoc($check_data);
        
        if ($count['count'] == 0) {
            $mensagens[] = "A tabela 'registro_horas' está vazia. Inserindo dados de exemplo...";
            
            // Verificar tabelas relacionadas
            $check_obras = mysqli_query($conn, "SELECT COUNT(*) as count FROM obras");
            $obras_count = mysqli_fetch_assoc($check_obras);
            
            $check_users = mysqli_query($conn, "SELECT COUNT(*) as count FROM utilizadores");
            $users_count = mysqli_fetch_assoc($check_users);
            
            if ($obras_count['count'] > 0 && $users_count['count'] > 0) {
                // Determinar os nomes corretos das colunas
                $id_obra_col = in_array('id_obra', $colunas) ? 'id_obra' : 'obra_id';
                $id_usuario_col = in_array('id_usuario', $colunas) ? 'id_usuario' : 
                                 (in_array('id_utilizador', $colunas) ? 'id_utilizador' : 'usuario_id');
                $data_col = in_array('data_registro', $colunas) ? 'data_registro' : 
                           (in_array('data_registo', $colunas) ? 'data_registo' : 'data');
                
                // Verificar colunas nas tabelas relacionadas
                $check_obras_cols = mysqli_query($conn, "DESCRIBE obras");
                $colunas_obras = [];
                while ($col = mysqli_fetch_assoc($check_obras_cols)) {
                    $colunas_obras[] = $col['Field'];
                }
                
                $id_obra_tab = in_array('obras_id', $colunas_obras) ? 'obras_id' : 
                              (in_array('id_obra', $colunas_obras) ? 'id_obra' : 'id');
                
                $check_users_cols = mysqli_query($conn, "DESCRIBE utilizadores");
                $colunas_users = [];
                while ($col = mysqli_fetch_assoc($check_users_cols)) {
                    $colunas_users[] = $col['Field'];
                }
                
                $id_user_tab = in_array('id_utilizadores', $colunas_users) ? 'id_utilizadores' : 'id';
                
                // Obter IDs para inserção
                $get_obra = mysqli_query($conn, "SELECT $id_obra_tab FROM obras LIMIT 1");
                $obra = mysqli_fetch_assoc($get_obra);
                
                $get_user = mysqli_query($conn, "SELECT $id_user_tab FROM utilizadores LIMIT 1");
                $user = mysqli_fetch_assoc($get_user);
                
                if ($obra && $user) {
                    $id_obra = $obra[$id_obra_tab];
                    $id_user = $user[$id_user_tab];
                    
                    // Inserir dados de exemplo
                    $data_hoje = date('Y-m-d');
                    $data_ontem = date('Y-m-d', strtotime('-1 day'));
                    
                    $insert_data = "INSERT INTO registro_horas ($id_obra_col, $id_usuario_col, horas, $data_col, descricao) VALUES 
                                   ($id_obra, $id_user, 8.5, '$data_hoje', 'Trabalho na fundação'),
                                   ($id_obra, $id_user, 6.0, '$data_ontem', 'Instalação elétrica')";
                    
                    if (mysqli_query($conn, $insert_data)) {
                        $mensagens[] = "Dados de exemplo inseridos com sucesso.";
                    } else {
                        $mensagens[] = "Erro ao inserir dados: " . mysqli_error($conn);
                    }
                } else {
                    $mensagens[] = "Não foi possível obter IDs válidos de obras e utilizadores.";
                }
            } else {
                $mensagens[] = "Não há obras ou utilizadores cadastrados para criar registros de exemplo.";
            }
        } else {
            $mensagens[] = "A tabela 'registro_horas' contém {$count['count']} registros.";
        }
    }
    
    // 4. Verificar consulta usada no dashboard
    $mensagens[] = "Testando consulta do dashboard...";
    
    // Determinar os nomes corretos das colunas
    $check_columns = mysqli_query($conn, "DESCRIBE registro_horas");
    $colunas = [];
    while ($col = mysqli_fetch_assoc($check_columns)) {
        $colunas[] = $col['Field'];
    }
    
    $id_obra_col = in_array('id_obra', $colunas) ? 'id_obra' : 'obra_id';
    $id_usuario_col = in_array('id_usuario', $colunas) ? 'id_usuario' : 
                     (in_array('id_utilizador', $colunas) ? 'id_utilizador' : 'usuario_id');
    $data_col = in_array('data_registro', $colunas) ? 'data_registro' : 
               (in_array('data_registo', $colunas) ? 'data_registo' : 'data');
    
    // Verificar tabelas relacionadas
    $check_obras_cols = mysqli_query($conn, "DESCRIBE obras");
    $colunas_obras = [];
    while ($col = mysqli_fetch_assoc($check_obras_cols)) {
        $colunas_obras[] = $col['Field'];
    }
    
    $id_obra_tab = in_array('obras_id', $colunas_obras) ? 'obras_id' : 
                  (in_array('id_obra', $colunas_obras) ? 'id_obra' : 'id');
    $nome_obra_col = in_array('nome_obra', $colunas_obras) ? 'nome_obra' : 'nome';
    
    $check_users_cols = mysqli_query($conn, "DESCRIBE utilizadores");
    $colunas_users = [];
    while ($col = mysqli_fetch_assoc($check_users_cols)) {
        $colunas_users[] = $col['Field'];
    }
    
    $id_user_tab = in_array('id_utilizadores', $colunas_users) ? 'id_utilizadores' : 'id';
    $nome_user_col = in_array('nome_utilizador', $colunas_users) ? 'nome_utilizador' : 'nome';
    
    // Testar a consulta
    $query = "SELECT rh.*, o.$nome_obra_col as nome_obra, u.$nome_user_col as nome_utilizador 
             FROM registro_horas rh 
             LEFT JOIN obras o ON rh.$id_obra_col = o.$id_obra_tab 
             LEFT JOIN utilizadores u ON rh.$id_usuario_col = u.$id_user_tab 
             ORDER BY rh.$data_col DESC 
             LIMIT 10";
    
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        $mensagens[] = "Consulta executada com sucesso. Encontrados " . mysqli_num_rows($result) . " registros.";
        
        // Mostrar os primeiros registros
        $registros = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $registros[] = [
                'id' => $row['id'] ?? 'N/A',
                'obra' => $row['nome_obra'] ?? 'N/A',
                'usuario' => $row['nome_utilizador'] ?? 'N/A',
                'data' => isset($row[$data_col]) ? date('d/m/Y', strtotime($row[$data_col])) : 'N/A',
                'horas' => $row['horas'] ?? 'N/A'
            ];
        }
        
        $mensagens[] = "Primeiros registros: " . json_encode($registros);
    } else {
        $mensagens[] = "Erro na consulta: " . mysqli_error($conn);
    }
    
    mysqli_close($conn);
    return $mensagens;
}

// Executar a verificação se o script for chamado diretamente
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $mensagens = verificarECorrigirRegistroHoras();
    
    echo "<html><head><title>Verificação da Tabela de Registro de Horas</title>";
    echo "<style>body{font-family:Arial,sans-serif;margin:20px;line-height:1.6}
          h1{color:#333}
          .success{color:green}
          .error{color:red}
          .info{color:blue}
          pre{background:#f4f4f4;padding:10px;border-radius:5px;overflow:auto}</style>";
    echo "</head><body>";
    echo "<h1>Verificação da Tabela de Registro de Horas</h1>";
    
    foreach ($mensagens as $msg) {
        if (strpos($msg, 'Erro') !== false) {
            echo "<p class='error'>$msg</p>";
        } elseif (strpos($msg, 'sucesso') !== false) {
            echo "<p class='success'>$msg</p>";
        } else {
            echo "<p class='info'>$msg</p>";
        }
    }
    
    echo "<p><a href='Projeto pag 5.php'>Voltar para o Dashboard</a></p>";
    echo "</body></html>";
}
?>