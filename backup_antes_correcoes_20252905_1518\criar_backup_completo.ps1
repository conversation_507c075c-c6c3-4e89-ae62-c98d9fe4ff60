# =====================================================
# SCRIPT DE BACKUP COMPLETO DA APLICAÇÃO
# =====================================================
# Este script cria um backup completo da aplicação e banco de dados
# antes de aplicar as correções críticas

param(
    [string]$BackupPath = "",
    [switch]$IncludeDatabase = $true,
    [switch]$CompressBackup = $true
)

# Configurações
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$projectRoot = Get-Location
$defaultBackupPath = "backup_completo_$timestamp"

if ($BackupPath -eq "") {
    $BackupPath = $defaultBackupPath
}

# Cores para output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }

# Função para criar diretório se não existir
function Ensure-Directory($path) {
    if (!(Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force | Out-Null
        Write-Info "Diretório criado: $path"
    }
}

# Função para copiar arquivo com verificação
function Backup-File($source, $destination) {
    try {
        if (Test-Path $source) {
            $destDir = Split-Path $destination -Parent
            Ensure-Directory $destDir
            Copy-Item $source $destination -Force
            Write-Success "✓ $source"
            return $true
        } else {
            Write-Warning "⚠ Arquivo não encontrado: $source"
            return $false
        }
    } catch {
        Write-Error "✗ Erro ao copiar $source : $($_.Exception.Message)"
        return $false
    }
}

# Função para copiar diretório
function Backup-Directory($source, $destination) {
    try {
        if (Test-Path $source) {
            Ensure-Directory $destination
            Copy-Item $source $destination -Recurse -Force
            Write-Success "✓ Diretório: $source"
            return $true
        } else {
            Write-Warning "⚠ Diretório não encontrado: $source"
            return $false
        }
    } catch {
        Write-Error "✗ Erro ao copiar diretório $source : $($_.Exception.Message)"
        return $false
    }
}

# Início do script
Write-Info "=============================================="
Write-Info "    BACKUP COMPLETO DA APLICAÇÃO"
Write-Info "=============================================="
Write-Info "Timestamp: $timestamp"
Write-Info "Diretório de backup: $BackupPath"
Write-Info "Incluir banco de dados: $IncludeDatabase"
Write-Info "Comprimir backup: $CompressBackup"
Write-Info "=============================================="

# Criar diretório principal de backup
Ensure-Directory $BackupPath

$successCount = 0
$totalFiles = 0

# =====================================================
# 1. BACKUP DOS ARQUIVOS DA APLICAÇÃO
# =====================================================

Write-Info ""
Write-Info "1. FAZENDO BACKUP DOS ARQUIVOS DA APLICAÇÃO..."
Write-Info "=============================================="

# Backup da pasta PHP PROJETO completa
Write-Info "Copiando pasta PHP PROJETO..."
if (Backup-Directory "PHP PROJETO" "$BackupPath\PHP PROJETO") {
    $successCount++
}
$totalFiles++

# Backup dos scripts de limpeza
Write-Info ""
Write-Info "Copiando scripts de limpeza e configuração..."
$scriptFiles = @(
    "criar_backup_antes_limpeza.ps1",
    "limpeza_segura_ficheiros.ps1",
    "LEIA-ME_LIMPEZA_FICHEIROS.md"
)

foreach ($file in $scriptFiles) {
    if (Backup-File $file "$BackupPath\$file") {
        $successCount++
    }
    $totalFiles++
}

# Backup dos backups anteriores (se existirem)
Write-Info ""
Write-Info "Verificando backups anteriores..."
$backupDirs = Get-ChildItem -Directory -Name "backup_*" | Where-Object { $_ -ne $BackupPath }
foreach ($dir in $backupDirs) {
    Write-Info "Incluindo backup anterior: $dir"
    if (Backup-Directory $dir "$BackupPath\backups_anteriores\$dir") {
        $successCount++
    }
    $totalFiles++
}

# =====================================================
# 2. BACKUP DO BANCO DE DADOS
# =====================================================

if ($IncludeDatabase) {
    Write-Info ""
    Write-Info "2. FAZENDO BACKUP DO BANCO DE DADOS..."
    Write-Info "=============================================="
    
    # Criar diretório para backup do banco
    $dbBackupDir = "$BackupPath\database"
    Ensure-Directory $dbBackupDir
    
    # Ler configurações do banco de dados
    $configFile = "PHP PROJETO\php\config.php"
    if (Test-Path $configFile) {
        Write-Info "Lendo configurações do banco de dados..."
        $configContent = Get-Content $configFile -Raw
        
        # Extrair configurações (método simples)
        if ($configContent -match "DB_SERVER.*?'([^']+)'") { $dbServer = $matches[1] }
        if ($configContent -match "DB_USERNAME.*?'([^']+)'") { $dbUser = $matches[1] }
        if ($configContent -match "DB_PASSWORD.*?'([^']+)'") { $dbPassword = $matches[1] }
        if ($configContent -match "DB_NAME.*?'([^']+)'") { $dbName = $matches[1] }
        
        Write-Info "Servidor: $dbServer"
        Write-Info "Usuário: $dbUser"
        Write-Info "Banco: $dbName"
        
        # Criar script SQL para backup manual
        $backupSqlScript = @"
-- =====================================================
-- BACKUP MANUAL DO BANCO DE DADOS
-- =====================================================
-- Execute este script no phpMyAdmin ou MySQL Workbench
-- Data: $(Get-Date)
-- Banco: $dbName
-- =====================================================

-- 1. Via phpMyAdmin:
-- - Acesse phpMyAdmin
-- - Selecione o banco '$dbName'
-- - Vá para 'Exportar'
-- - Escolha 'Método personalizado'
-- - Marque todas as tabelas
-- - Formato: SQL
-- - Clique em 'Executar'
-- - Salve como: backup_$($dbName)_$timestamp.sql

-- 2. Via linha de comando:
-- mysqldump -u $dbUser -p $dbName > backup_$($dbName)_$timestamp.sql

-- 3. Estrutura esperada do banco:
-- Tabelas principais:
-- - obras
-- - utilizadores  
-- - registro_horas
-- - orcamentos
-- - materiais

-- =====================================================
-- COMANDOS PARA RESTAURAÇÃO (SE NECESSÁRIO)
-- =====================================================

-- Via phpMyAdmin:
-- - Acesse phpMyAdmin
-- - Selecione o banco '$dbName'
-- - Vá para 'Importar'
-- - Escolha o arquivo de backup
-- - Clique em 'Executar'

-- Via linha de comando:
-- mysql -u $dbUser -p $dbName < backup_$($dbName)_$timestamp.sql

-- =====================================================
"@
        
        $backupSqlScript | Out-File "$dbBackupDir\INSTRUCOES_BACKUP_DATABASE.sql" -Encoding UTF8
        Write-Success "✓ Instruções de backup do banco criadas"
        
        # Tentar fazer backup automático se mysqldump estiver disponível
        try {
            $mysqldumpPath = Get-Command mysqldump -ErrorAction SilentlyContinue
            if ($mysqldumpPath) {
                Write-Info "mysqldump encontrado. Tentando backup automático..."
                $backupFile = "$dbBackupDir\backup_$($dbName)_$timestamp.sql"
                
                # Executar mysqldump
                $arguments = @(
                    "-u", $dbUser,
                    "-p$dbPassword",
                    "--single-transaction",
                    "--routines",
                    "--triggers",
                    $dbName
                )
                
                Start-Process -FilePath "mysqldump" -ArgumentList $arguments -RedirectStandardOutput $backupFile -Wait -NoNewWindow
                
                if (Test-Path $backupFile) {
                    $fileSize = (Get-Item $backupFile).Length
                    if ($fileSize -gt 0) {
                        Write-Success "✓ Backup automático do banco criado: $backupFile"
                        Write-Success "  Tamanho: $([math]::Round($fileSize/1KB, 2)) KB"
                        $successCount++
                    } else {
                        Write-Warning "⚠ Backup automático criado mas está vazio"
                        Remove-Item $backupFile -Force
                    }
                } else {
                    Write-Warning "⚠ Backup automático falhou"
                }
            } else {
                Write-Warning "⚠ mysqldump não encontrado. Use as instruções manuais."
            }
        } catch {
            Write-Warning "⚠ Erro no backup automático: $($_.Exception.Message)"
            Write-Info "Use as instruções manuais em INSTRUCOES_BACKUP_DATABASE.sql"
        }
        
        $totalFiles++
        
    } else {
        Write-Error "✗ Arquivo de configuração não encontrado: $configFile"
    }
}

# =====================================================
# 3. CRIAR ARQUIVO DE INFORMAÇÕES DO BACKUP
# =====================================================

Write-Info ""
Write-Info "3. CRIANDO INFORMAÇÕES DO BACKUP..."
Write-Info "=============================================="

$backupInfo = @"
# BACKUP COMPLETO DA APLICAÇÃO
## Informações do Backup

**Data/Hora:** $(Get-Date)
**Versão:** Backup antes das correções críticas
**Diretório:** $BackupPath

## Conteúdo do Backup

### Arquivos da Aplicação
- ✅ PHP PROJETO/ (pasta completa)
- ✅ Scripts de limpeza e configuração
- ✅ Backups anteriores (se existirem)

### Banco de Dados
$(if ($IncludeDatabase) { "- Instruções de backup incluídas" } else { "- Não incluído" })
$(if ($IncludeDatabase) { "- Localização: database/" } else { "" })

## Como Restaurar

### 1. Restaurar Arquivos
```powershell
# Copiar arquivos de volta
Copy-Item "$BackupPath\PHP PROJETO" "." -Recurse -Force
```

### 2. Restaurar Banco de Dados
```sql
-- Via phpMyAdmin ou linha de comando
-- Consulte: database/INSTRUCOES_BACKUP_DATABASE.sql
```

## Estatísticas
- **Arquivos processados:** $totalFiles
- **Sucessos:** $successCount
- **Falhas:** $($totalFiles - $successCount)

## Próximos Passos
1. Verificar se o backup está completo
2. Aplicar as correções críticas usando os scripts SQL
3. Testar a aplicação após as correções
4. Manter este backup por segurança

---
**IMPORTANTE:** Este backup foi criado automaticamente antes da aplicação das correções críticas.
"@

$backupInfo | Out-File "$BackupPath\README_BACKUP.md" -Encoding UTF8
Write-Success "✓ Arquivo de informações criado"

# =====================================================
# 4. COMPRIMIR BACKUP (OPCIONAL)
# =====================================================

if ($CompressBackup) {
    Write-Info ""
    Write-Info "4. COMPRIMINDO BACKUP..."
    Write-Info "=============================================="
    
    try {
        $zipFile = "$BackupPath.zip"
        Compress-Archive -Path $BackupPath -DestinationPath $zipFile -Force
        
        if (Test-Path $zipFile) {
            $zipSize = (Get-Item $zipFile).Length
            Write-Success "✓ Backup comprimido criado: $zipFile"
            Write-Success "  Tamanho: $([math]::Round($zipSize/1MB, 2)) MB"
            
            # Perguntar se quer manter a pasta descomprimida
            $response = Read-Host "Manter pasta descomprimida? (s/N)"
            if ($response -notmatch '^[sS]') {
                Remove-Item $BackupPath -Recurse -Force
                Write-Info "Pasta descomprimida removida. Backup mantido em: $zipFile"
            }
        }
    } catch {
        Write-Error "✗ Erro ao comprimir backup: $($_.Exception.Message)"
    }
}

# =====================================================
# 5. RELATÓRIO FINAL
# =====================================================

Write-Info ""
Write-Info "=============================================="
Write-Info "           BACKUP CONCLUÍDO"
Write-Info "=============================================="
Write-Success "✅ Backup criado com sucesso!"
Write-Info ""
Write-Info "📊 ESTATÍSTICAS:"
Write-Info "   • Arquivos processados: $totalFiles"
Write-Info "   • Sucessos: $successCount"
Write-Info "   • Falhas: $($totalFiles - $successCount)"
Write-Info ""
Write-Info "📁 LOCALIZAÇÃO:"
if ($CompressBackup -and (Test-Path "$BackupPath.zip")) {
    Write-Info "   • Backup comprimido: $BackupPath.zip"
}
if (Test-Path $BackupPath) {
    Write-Info "   • Backup descomprimido: $BackupPath"
}
Write-Info ""
Write-Info "📋 PRÓXIMOS PASSOS:"
Write-Info "   1. Verificar se o backup está completo"
Write-Info "   2. Aplicar correções críticas (SQL/EXECUTAR_CORRECOES_CRITICAS.sql)"
Write-Info "   3. Testar a aplicação"
Write-Info "   4. Manter este backup por segurança"
Write-Info ""
Write-Success "🎉 Backup concluído! Agora você pode aplicar as correções com segurança."
Write-Info "=============================================="
