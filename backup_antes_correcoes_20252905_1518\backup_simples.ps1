# Script simples para backup da aplicação
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "backup_antes_correcoes_$timestamp"

Write-Host "=== CRIANDO BACKUP DA APLICAÇÃO ===" -ForegroundColor Green
Write-Host "Diretório de backup: $backupDir" -ForegroundColor Cyan

# Criar diretório de backup
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# Copiar pasta PHP PROJETO
Write-Host "Copiando PHP PROJETO..." -ForegroundColor Yellow
Copy-Item "PHP PROJETO" "$backupDir\PHP PROJETO" -Recurse -Force

# Copiar scripts
Write-Host "Copiando scripts..." -ForegroundColor Yellow
Copy-Item "*.ps1" $backupDir -Force -ErrorAction SilentlyContinue
Copy-Item "*.md" $backupDir -Force -ErrorAction SilentlyContinue

# Criar instruções de backup do banco
$dbInstructions = @"
INSTRUÇÕES PARA BACKUP DO BANCO DE DADOS

1. Abra phpMyAdmin (http://localhost/phpmyadmin)
2. Faça login (usuário: root, senha: IEFP2025*)
3. Selecione o banco 'controle_obras'
4. Clique em 'Exportar'
5. Escolha 'Método personalizado'
6. Marque todas as tabelas
7. Formato: SQL
8. Clique em 'Executar'
9. Salve como: backup_controle_obras_$timestamp.sql

IMPORTANTE: Salve o arquivo .sql nesta pasta de backup!
"@

$dbInstructions | Out-File "$backupDir\BACKUP_BANCO_INSTRUCOES.txt" -Encoding UTF8

# Criar arquivo de informações
$info = @"
BACKUP CRIADO EM: $(Get-Date)
PASTA DE BACKUP: $backupDir

CONTEÚDO:
- PHP PROJETO/ (aplicação completa)
- Scripts .ps1 e .md
- BACKUP_BANCO_INSTRUCOES.txt

PRÓXIMOS PASSOS:
1. Fazer backup do banco seguindo as instruções
2. Aplicar correções críticas (SQL/EXECUTAR_CORRECOES_CRITICAS.sql)
3. Testar aplicação
"@

$info | Out-File "$backupDir\README.txt" -Encoding UTF8

Write-Host ""
Write-Host "=== BACKUP CONCLUÍDO ===" -ForegroundColor Green
Write-Host "Localização: $backupDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "PRÓXIMOS PASSOS:" -ForegroundColor Yellow
Write-Host "1. Fazer backup do banco (veja BACKUP_BANCO_INSTRUCOES.txt)" -ForegroundColor White
Write-Host "2. Aplicar correções críticas" -ForegroundColor White
Write-Host "3. Testar aplicação" -ForegroundColor White
Write-Host ""
Write-Host "Backup criado com sucesso!" -ForegroundColor Green
