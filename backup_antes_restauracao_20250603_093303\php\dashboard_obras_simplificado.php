<?php
// Vers<PERSON> simplificada do dashboard_obras.php
require_once 'conexao.php';

function getDashboardData() {
    $conn = connectToDatabase();
    $data = array();
    
    // Total de Obras
    $sql = "SELECT COUNT(*) as total FROM obras";
    $result = $conn->query($sql);
    if ($result && $row = $result->fetch_assoc()) {
        $data['total_obras'] = $row['total'];
    } else {
        $data['total_obras'] = 0;
    }
    
    // Obras em Andamento
    $sql = "SELECT COUNT(*) as total FROM obras WHERE status = 'Em andamento'";
    $result = $conn->query($sql);
    if ($result && $row = $result->fetch_assoc()) {
        $data['obras_em_andamento'] = $row['total'];
    } else {
        $data['obras_em_andamento'] = 0;
    }
    
    // Orçamento Total
    $sql = "SELECT COALESCE(SUM(orcamento), 0) as total FROM obras";
    $result = $conn->query($sql);
    if ($result && $row = $result->fetch_assoc()) {
        $data['orcamento_total'] = (float)$row['total'];
    } else {
        $data['orcamento_total'] = 0;
    }
    
    // Status das Obras
    $sql = "SELECT nome_obra, status, progresso, orcamento FROM obras 
            ORDER BY FIELD(status, 'Em andamento', 'Concluída', 'Pausada', 'Cancelada') 
            LIMIT 50";
    $result = $conn->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            // Definir progresso como 100% para obras concluídas
            if (isset($row['status']) && $row['status'] == 'Concluída') {
                $row['progresso'] = 100;
            } elseif (!isset($row['progresso']) || $row['progresso'] === null) {
                $row['progresso'] = 0;
            }
            $data['obras_progresso'][] = $row;
        }
    }
    
    // Registros de Horas
    try {
        // Primeiro, verificar se a tabela registro_horas existe
        $result = $conn->query("SHOW TABLES LIKE 'registro_horas'");
        if ($result && $result->num_rows > 0) {
            // Verificar os nomes das colunas para construir a consulta correta
            $result_cols = $conn->query("SHOW COLUMNS FROM registro_horas");
            $colunas = [];
            if ($result_cols) {
                while ($col = $result_cols->fetch_assoc()) {
                    $colunas[] = $col['Field'];
                }
            }
            
            // Determinar os nomes corretos das colunas
            $id_obra_col = in_array('id_obra', $colunas) ? 'id_obra' : 'obra_id';
            $id_usuario_col = in_array('id_usuario', $colunas) ? 'id_usuario' : 
                             (in_array('id_utilizador', $colunas) ? 'id_utilizador' : 'usuario_id');
            $data_col = in_array('data_registro', $colunas) ? 'data_registro' : 'data';
            
            // Verificar também as colunas nas tabelas obras e utilizadores
            $result_obras = $conn->query("SHOW COLUMNS FROM obras");
            $colunas_obras = [];
            if ($result_obras) {
                while ($col = $result_obras->fetch_assoc()) {
                    $colunas_obras[] = $col['Field'];
                }
            }
            
            $id_obra_tab = in_array('obras_id', $colunas_obras) ? 'obras_id' : 'id_obra';
            $nome_obra_col = in_array('nome_obra', $colunas_obras) ? 'nome_obra' : 'nome';
            
            $result_users = $conn->query("SHOW COLUMNS FROM utilizadores");
            $colunas_users = [];
            if ($result_users) {
                while ($col = $result_users->fetch_assoc()) {
                    $colunas_users[] = $col['Field'];
                }
            }
            
            $id_user_tab = in_array('id_utilizadores', $colunas_users) ? 'id_utilizadores' : 'id';
            $nome_user_col = in_array('nome_utilizador', $colunas_users) ? 'nome_utilizador' : 'nome';
            
            // Construir a consulta com os nomes corretos das colunas
            $sql = "SELECT rh.id, rh.$data_col as data_registro, rh.horas, rh.descricao, 
                    o.$nome_obra_col as nome_obra, u.$nome_user_col as nome_utilizador 
                    FROM registro_horas rh 
                    LEFT JOIN obras o ON rh.$id_obra_col = o.$id_obra_tab 
                    LEFT JOIN utilizadores u ON rh.$id_usuario_col = u.$id_user_tab 
                    ORDER BY rh.$data_col DESC 
                    LIMIT 10";
            
            $result = $conn->query($sql);
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $data['registros_horas'][] = [
                        'id' => $row['id'] ?? '',
                        'data_registro' => $row['data_registro'] ?? '',
                        'horas' => $row['horas'] ?? '',
                        'descricao' => $row['descricao'] ?? '',
                        'nome_obra' => $row['nome_obra'] ?? 'Sem nome',
                        'nome_utilizador' => $row['nome_utilizador'] ?? 'Sem nome'
                    ];
                }
            }
        }
    } catch (Exception $e) {
        error_log("Erro ao buscar registros de horas: " . $e->getMessage());
    }
    
    $conn->close();
    return $data;
}
?>


