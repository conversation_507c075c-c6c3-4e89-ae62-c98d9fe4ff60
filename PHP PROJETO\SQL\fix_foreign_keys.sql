-- =====================================================
-- SCRIPT PARA CORRIGIR FOREIGN KEYS INCONSISTENTES
-- =====================================================
-- Este script corrige as referências de chaves estrangeiras
-- sem alterar a funcionalidade da aplicação

-- =====================================================
-- 1. VERIFICAR E CORRIGIR ESTRUTURA ATUAL
-- =====================================================

-- Desabilitar verificação de foreign keys temporariamente
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 2. REMOVER FOREIGN KEYS CONFLITANTES
-- =====================================================

-- Função para remover constraint se existir
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS DropConstraintIfExists(
    IN tableName VARCHAR(64),
    IN constraintName VARCHAR(64)
)
BEGIN
    DECLARE constraintExists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO constraintExists
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = tableName
    AND CONSTRAINT_NAME = constraintName;
    
    IF constraintExists > 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tableName, '` DROP FOREIGN KEY `', constraintName, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- Remover constraints conflitantes conhecidas
CALL DropConstraintIfExists('registro_horas', 'fk_registro_obra');
CALL DropConstraintIfExists('registro_horas', 'fk_registro_usuario');
CALL DropConstraintIfExists('orcamentos', 'fk_orcamentos_obras');
CALL DropConstraintIfExists('materiais', 'fk_materiais_obras');
CALL DropConstraintIfExists('materiais', 'materiais_ibfk_1');

-- =====================================================
-- 3. VERIFICAR E CORRIGIR ESTRUTURA DAS TABELAS
-- =====================================================

-- Garantir que a tabela obras tenha a estrutura correta
ALTER TABLE `obras` 
MODIFY COLUMN `obras_id` INT NOT NULL AUTO_INCREMENT,
MODIFY COLUMN `nome_obra` VARCHAR(255) NOT NULL,
MODIFY COLUMN `endereço` VARCHAR(255) NOT NULL,
MODIFY COLUMN `data_inicio` DATE NOT NULL,
MODIFY COLUMN `data_fim` DATE NULL,
MODIFY COLUMN `status` VARCHAR(50) DEFAULT 'Em andamento',
ADD COLUMN IF NOT EXISTS `descricao` TEXT NULL,
ADD COLUMN IF NOT EXISTS `orcamento` DECIMAL(15, 2) NULL,
ADD COLUMN IF NOT EXISTS `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Garantir que a tabela utilizadores tenha a estrutura correta
ALTER TABLE `utilizadores`
MODIFY COLUMN `id_utilizadores` INT NOT NULL AUTO_INCREMENT,
MODIFY COLUMN `nome_utilizador` VARCHAR(255) NOT NULL,
MODIFY COLUMN `password` VARCHAR(255) NOT NULL,
MODIFY COLUMN `cargo_utilizador` VARCHAR(50) NOT NULL,
ADD COLUMN IF NOT EXISTS `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Garantir que a tabela registro_horas tenha a estrutura correta
ALTER TABLE `registro_horas`
MODIFY COLUMN `id` INT NOT NULL AUTO_INCREMENT,
MODIFY COLUMN `id_obra` INT NOT NULL,
MODIFY COLUMN `id_usuario` INT NOT NULL,
MODIFY COLUMN `horas` DECIMAL(5, 2) NOT NULL,
MODIFY COLUMN `data_registro` DATE NOT NULL,
MODIFY COLUMN `descricao` TEXT NULL,
ADD COLUMN IF NOT EXISTS `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Garantir que a tabela orcamentos tenha a estrutura correta
ALTER TABLE `orcamentos`
MODIFY COLUMN `id` INT NOT NULL AUTO_INCREMENT,
MODIFY COLUMN `obra_id` INT NOT NULL,
MODIFY COLUMN `descricao` TEXT NOT NULL,
MODIFY COLUMN `valor` DECIMAL(15, 2) NOT NULL,
MODIFY COLUMN `status` VARCHAR(50) DEFAULT 'Em análise',
ADD COLUMN IF NOT EXISTS `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS `data_aprovacao` DATE NULL;

-- Garantir que a tabela materiais tenha a estrutura correta
ALTER TABLE `materiais`
MODIFY COLUMN `material_id` INT NOT NULL AUTO_INCREMENT,
MODIFY COLUMN `nome` VARCHAR(255) NOT NULL,
MODIFY COLUMN `quantidade` DECIMAL(10, 2) NOT NULL,
MODIFY COLUMN `obras_id` INT NULL,
ADD COLUMN IF NOT EXISTS `unidade_medida` VARCHAR(50) DEFAULT 'unidades',
ADD COLUMN IF NOT EXISTS `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- =====================================================
-- 4. LIMPAR DADOS ÓRFÃOS
-- =====================================================

-- Remover registros de horas que referenciam obras inexistentes
DELETE FROM `registro_horas` 
WHERE `id_obra` NOT IN (SELECT `obras_id` FROM `obras`);

-- Remover registros de horas que referenciam usuários inexistentes
DELETE FROM `registro_horas` 
WHERE `id_usuario` NOT IN (SELECT `id_utilizadores` FROM `utilizadores`);

-- Remover orçamentos que referenciam obras inexistentes
DELETE FROM `orcamentos` 
WHERE `obra_id` NOT IN (SELECT `obras_id` FROM `obras`);

-- Limpar referências de materiais para obras inexistentes
UPDATE `materiais` 
SET `obras_id` = NULL 
WHERE `obras_id` IS NOT NULL 
AND `obras_id` NOT IN (SELECT `obras_id` FROM `obras`);

-- =====================================================
-- 5. RECRIAR FOREIGN KEYS CORRETAS
-- =====================================================

-- Foreign key: registro_horas -> obras
ALTER TABLE `registro_horas` 
ADD CONSTRAINT `fk_registro_horas_obras` 
FOREIGN KEY (`id_obra`) REFERENCES `obras` (`obras_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Foreign key: registro_horas -> utilizadores
ALTER TABLE `registro_horas` 
ADD CONSTRAINT `fk_registro_horas_utilizadores` 
FOREIGN KEY (`id_usuario`) REFERENCES `utilizadores` (`id_utilizadores`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Foreign key: orcamentos -> obras
ALTER TABLE `orcamentos` 
ADD CONSTRAINT `fk_orcamentos_obras` 
FOREIGN KEY (`obra_id`) REFERENCES `obras` (`obras_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Foreign key: materiais -> obras (SET NULL para permitir materiais sem obra)
ALTER TABLE `materiais` 
ADD CONSTRAINT `fk_materiais_obras` 
FOREIGN KEY (`obras_id`) REFERENCES `obras` (`obras_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =====================================================
-- 6. REABILITAR VERIFICAÇÃO DE FOREIGN KEYS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 7. VALIDAR INTEGRIDADE DOS DADOS
-- =====================================================

-- Verificar se todas as foreign keys estão funcionando
SELECT 
    'registro_horas' as tabela,
    COUNT(*) as total_registros,
    COUNT(DISTINCT id_obra) as obras_referenciadas,
    COUNT(DISTINCT id_usuario) as usuarios_referenciados
FROM registro_horas

UNION ALL

SELECT 
    'orcamentos' as tabela,
    COUNT(*) as total_registros,
    COUNT(DISTINCT obra_id) as obras_referenciadas,
    0 as usuarios_referenciados
FROM orcamentos

UNION ALL

SELECT 
    'materiais' as tabela,
    COUNT(*) as total_registros,
    COUNT(DISTINCT obras_id) as obras_referenciadas,
    0 as usuarios_referenciados
FROM materiais;

-- =====================================================
-- 8. LIMPAR PROCEDIMENTOS TEMPORÁRIOS
-- =====================================================

DROP PROCEDURE IF EXISTS DropConstraintIfExists;

-- =====================================================
-- SCRIPT CONCLUÍDO
-- =====================================================
-- Foreign keys corrigidas e padronizadas
-- Integridade referencial garantida
-- =====================================================
