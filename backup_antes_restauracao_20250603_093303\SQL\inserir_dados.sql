-- Inserir algumas obras de exemplo se a tabela estiver vazia
INSERT INTO obras (nome_obra, endereço, data_inicio, data_fim)
SELECT * FROM (
    SELECT 'Construção de Prédio Residencial', 'Lisboa', '2023-01-15', '2024-06-30'
) AS tmp
WHERE NOT EXISTS (
    SELECT nome_obra FROM obras WHERE nome_obra = 'Construção de Prédio Residencial'
) LIMIT 1;

INSERT INTO obras (nome_obra, endereço, data_inicio, data_fim)
SELECT * FROM (
    SELECT 'Reforma de Escritório', 'Porto', '2023-03-10', '2023-09-15'
) AS tmp
WHERE NOT EXISTS (
    SELECT nome_obra FROM obras WHERE nome_obra = 'Reforma de Escritório'
) LIMIT 1;

INSERT INTO obras (nome_obra, endereço, data_inicio, data_fim)
SELECT * FROM (
    SELECT 'Construção de Moradia', 'Faro', '2023-05-20', '2024-02-28'
) AS tmp
WHERE NOT EXISTS (
    SELECT nome_obra FROM obras WHERE nome_obra = 'Construção de Moradia'
) LIMIT 1;
