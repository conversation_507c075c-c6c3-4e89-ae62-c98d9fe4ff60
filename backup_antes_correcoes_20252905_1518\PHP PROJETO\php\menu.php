<?php
session_start();
require_once 'verificar_permissao.php';
?>
<nav class="navbar">
    <div class="logo">
        <img src="Imagem1.png" alt="Logo">
        <span>Built Organizer</span>
    </div>
    <ul class="nav-links">
        <li><a href="Projeto.php">INÍCIO</a></li>
        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                <li><a href="Projeto pag 2.php">OBRAS</a></li>
            <?php endif; ?>
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                <li><a href="Projeto pag 3.php">EQUIPAMENTOS</a></li>
            <?php endif; ?>
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                <li><a href="Projeto pag 4.php">HORAS</a></li>
            <?php endif; ?>
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                <li><a href="Projeto pag 5.php">GESTÃO</a></li>
            <?php endif; ?>
            <?php 
            // Verificação explícita para níveis 1 e 2
            if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] === '1' || $_SESSION['cargo_utilizador'] === '2')): 
            ?>
                <li><a href="Registar_utilizador.php">NOVO UTILIZADOR</a></li>
            <?php endif; ?>
        <?php endif; ?>
        <li><a href="sobre_nos.php">SOBRE NÓS</a></li>
        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
            <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
            <li><span class="user-info">Bem vindo <?php echo $_SESSION['userName']; ?> (Nível: <?php echo $_SESSION['cargo_utilizador']; ?>)</span></li>
        <?php else: ?>
            <li><a href="#" id="login-btn">LOGIN</a></li>
        <?php endif; ?>
    </ul>
</nav>

