<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header("Location: Projeto.php?msg=" . urlencode("Você não tem permissão para acessar esta página"));
    exit();
}

$conn = connectToDatabase();
$mensagem = '';
$tipo_mensagem = '';
$registro = null;

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['mensagem'] = "ID inválido.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

$id = intval($_GET['id']);

// Buscar os dados do registro
$query = "SELECT rh.*, o.nome_obra, u.nome_utilizador
          FROM registro_horas rh
          JOIN obras o ON rh.id_obra = o.obras_id
          JOIN utilizadores u ON rh.id_usuario = u.id_utilizadores
          WHERE rh.id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "i", $id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($registro = mysqli_fetch_assoc($result)) {
    // Registro encontrado, continuar com a página
} else {
    $_SESSION['mensagem'] = "Registro não encontrado.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

mysqli_stmt_close($stmt);

// Buscar lista de obras para o formulário
$query_obras = "SELECT obras_id, nome_obra FROM obras ORDER BY nome_obra";
$result_obras = mysqli_query($conn, $query_obras);

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Log para depuração
    error_log("Formulário enviado: " . print_r($_POST, true));
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $mensagem = "Erro de segurança. Por favor, tente novamente.";
        $tipo_mensagem = "danger";
    } else {
        // Obter e validar os dados do formulário
        $obra_id = isset($_POST['obra_id']) ? intval($_POST['obra_id']) : 0;
        $data = isset($_POST['data']) ? $_POST['data'] : '';
        $horas = isset($_POST['horas']) ? floatval($_POST['horas']) : 0;
        $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : '';

        // Escapar strings para evitar SQL injection
        $descricao_escaped = mysqli_real_escape_string($conn, $descricao);
        $data_escaped = mysqli_real_escape_string($conn, $data);

        // Atualizar o registro
        $query_update = "UPDATE registro_horas SET
                        id_obra = $obra_id,
                        data_registro = '$data_escaped',
                        horas = $horas,
                        descricao = '$descricao_escaped'
                        WHERE id = $id";

        // Log para depuração
        error_log("Query SQL: $query_update");

        $result = mysqli_query($conn, $query_update);
        if ($result) {
            // Log para depuração
            error_log("Atualização bem-sucedida!");
            $_SESSION['mensagem'] = "Registro atualizado com sucesso!";
            $_SESSION['tipo_mensagem'] = "success";
            header("Location: Projeto pag 4.php");
            exit();
        } else {
            // Log para depuração
            error_log("Erro na atualização: " . mysqli_error($conn));
            $mensagem = "Erro ao atualizar registro: " . mysqli_error($conn);
            $tipo_mensagem = "danger";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Registro de Horas - Built Organizer</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Ícones da biblioteca Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php" class="active">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">ADMINISTRAÇÃO</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Mensagens de alerta -->
            <?php if(!empty($mensagem)): ?>
                <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                    <?php echo $mensagem; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
            <?php endif; ?>

            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-clock-history"></i> Editar Registro de Horas</h1>
                    <p>Edite os detalhes do registro de horas trabalhadas.</p>
                </div>
            </div>

            <div class="container">
                <div class="card">
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="id" value="<?php echo $id; ?>">

                            <div class="mb-3">
                                <label for="obra_id" class="form-label">Obra</label>
                                <select class="form-select" id="obra_id" name="obra_id" required>
                                    <option value="">Selecione uma obra</option>
                                    <?php
                                    // Reset the result pointer
                                    mysqli_data_seek($result_obras, 0);
                                    while ($obra = mysqli_fetch_assoc($result_obras)):
                                    ?>
                                        <option value="<?php echo $obra['obras_id']; ?>" <?php echo ($obra['obras_id'] == $registro['id_obra']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($obra['nome_obra']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="funcionario" class="form-label">Funcionário</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($registro['nome_utilizador']); ?>" readonly>
                            </div>

                            <div class="mb-3">
                                <label for="data" class="form-label">Data</label>
                                <input type="date" class="form-control" id="data" name="data"
                                       value="<?php echo $registro['data_registro']; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="horas" class="form-label">Horas Trabalhadas</label>
                                <input type="number" class="form-control" id="horas" name="horas"
                                       step="0.5" min="0.5" max="24" value="<?php echo $registro['horas']; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="descricao" class="form-label">Descrição</label>
                                <textarea class="form-control" id="descricao" name="descricao"
                                          rows="3" required><?php echo htmlspecialchars($registro['descricao'] ?? ''); ?></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="Projeto pag 4.php" class="btn btn-secondary">Cancelar</a>
                                <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2024 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>
</body>
</html>
<?php mysqli_close($conn); ?>
