/* Estilos padronizados para o menu - Não fixo */
.Menu {
    background-color: #4C4C4C;
    color: white;
    width: 100%;
    position: relative; /* Alterado de fixed para relative */
    top: 0;
    left: 0;
    z-index: 100;
}

.Menu nav {
    width: 100%;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
}

.<PERSON>u ul {
    list-style: none;
    padding: 0;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 1200px;
}

.<PERSON>u li {
    padding: 0 15px;
    display: inline-block;
    text-align: center;
    margin: 0 5px;
}

.Menu li.logo-item {
    margin-right: 20px;
}

.Menu .logotipo {
    max-height: 40px;
    width: auto;
}

/* Estilo padrão para todos os links do menu */
.Menu a {
    color: white;
    text-decoration: none;
    display: block;
    font-weight: 500;
    transition: all 0.3s;
    padding: 10px 15px;
    text-align: center;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    border-radius: 0;
}

.Menu a:hover {
    color: #ddd;
    background-color: rgba(255, 255, 255, 0.1);
}

.Menu a.active {
    color: #fff;
    border-bottom: 2px solid white;
}

/* Estilo para o texto de boas-vindas */
.Menu .welcome-text {
    color: white;
    font-size: 0.8rem;
    padding: 10px 15px;
    display: block;
    text-align: center;
}

