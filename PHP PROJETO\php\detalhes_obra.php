<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Verificar se o ID da obra foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: Projeto pag 2.php");
    exit();
}

// Obter o ID da obra
$id_obra = $_GET['id'];

// Conectar ao banco de dados
$conn = connectToDatabase();

// Buscar dados da obra
$query = "SELECT * FROM obras WHERE obras_id = '$id_obra'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    header("Location: Projeto pag 2.php");
    exit();
}

$obra = mysqli_fetch_assoc($result);

mysqli_close($conn);
?>
<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Obra - Built Organizer</title>
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!--CSS da biblioteca Bootstrap-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
</head>
<body>
    <div class="wrapper">
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php" class="active">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li class="Orçamentos"><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li class="Horas"><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li class="Gestao"><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>


                        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
                            <li><a href="Projeto.php?action=logout" id="logout">Logout</a></li>
                            <li><span class="welcome-text">Bem vindo <?php echo $_SESSION['userName']; ?> (Nível: <?php echo $_SESSION['cargo_utilizador']; ?>)</span></li>
                        <?php else: ?>
                            <li><a href="Projeto.php" id="login-btn">Login</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <div class="container mt-4">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header" style="background-color: #4C4C4C; color: white;">
                                <h2 class="mb-0">Detalhes da Obra</h2>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h3><?php echo $obra['nome_obra']; ?></h3>
                                        <p><strong>Localização:</strong> <?php echo $obra['endereço']; ?></p>
                                        <p><strong>Data de Início:</strong> <?php echo date('d/m/Y', strtotime($obra['data_inicio'])); ?></p>
                                        <p><strong>Data de Conclusão Prevista:</strong> <?php echo $obra['data_fim'] ? date('d/m/Y', strtotime($obra['data_fim'])) : 'Não definida'; ?></p>
                                        <p><strong>Status:</strong> Em andamento</p>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <a href="Projeto pag 2.php" class="btn" style="background-color: #4C4C4C; color: white;">Voltar para Lista de Obras</a>

                                    <?php if (in_array($_SESSION['cargo_utilizador'], ['1', '2', '3'])): ?>
                                    <a href="Projeto pag 2.php?editar=<?php echo $obra['obras_id']; ?>" class="btn" style="background-color: #555; color: white;">Editar Obra</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>


