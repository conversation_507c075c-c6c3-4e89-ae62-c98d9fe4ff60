<?php
/**
 * Arquivo de configuração com credenciais do banco de dados
 * Este arquivo deve estar em um diretório seguro, fora do acesso web direto
 */

// Definir constantes para conexão com o banco de dados
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'IEFP2025*');
define('DB_NAME', 'controle_obras');
define('DB_CHARSET', 'utf8');

// Configurações de segurança
define('CSRF_TOKEN_SECRET', 'built_organizer_secure_token_2024');
define('SESSION_TIMEOUT', 3600); // 1 hora em segundos

// Função para regenerar o ID da sessão periodicamente
function regenerateSessionIfNeeded() {
    $regenerate_time = 1800; // 30 minutos
    
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
        session_regenerate_id(true);
        return;
    }
    
    if (time() - $_SESSION['last_regeneration'] > $regenerate_time) {
        $_SESSION['last_regeneration'] = time();
        session_regenerate_id(true);
    }
}

// Função para verificar timeout da sessão
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            // Sessão expirou
            session_unset();
            session_destroy();
            session_start();
            return false;
        }
    }
    
    $_SESSION['last_activity'] = time();
    return true;
}
?>

