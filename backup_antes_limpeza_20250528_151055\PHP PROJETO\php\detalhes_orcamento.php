<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';

// Verificar se o usuário está logado - CORRIGIDO
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Verificar permissão para acessar a página de orçamentos - CORRIGIDO
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')) {
    header("Location: Projeto.php?msg=Você não tem permissão para acessar esta página");
    exit;
}

// Verificar se o ID do orçamento foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['mensagem'] = "ID do orçamento não fornecido";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 3.php");
    exit;
}

$id_orcamento = $_GET['id'];
$conn = connectToDatabase();

// Adicionar verificação de conexão
if (!$conn) {
    $_SESSION['mensagem'] = "Erro de conexão com o banco de dados";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 3.php");
    exit;
}

// Buscar detalhes do orçamento
$query = "SELECT o.*, ob.nome_obra, ob.endereço, ob.data_inicio, ob.data_fim
          FROM orcamentos o
          JOIN obras ob ON o.obra_id = ob.obras_id
          WHERE o.id = ?";
$stmt = mysqli_prepare($conn, $query);

// Verificar se a preparação da consulta foi bem-sucedida
if (!$stmt) {
    $_SESSION['mensagem'] = "Erro ao preparar consulta: " . mysqli_error($conn);
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 3.php");
    exit;
}

mysqli_stmt_bind_param($stmt, "i", $id_orcamento);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['mensagem'] = "Orçamento não encontrado";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 3.php");
    exit;
}

$orcamento = mysqli_fetch_assoc($result);

// Função para definir a cor do status
function getStatusColor($status) {
    switch($status) {
        case 'Em análise':
            return 'info';
        case 'Aprovado':
            return 'success';
        case 'Rejeitado':
            return 'danger';
        case 'Concluído':
            return 'primary';
        default:
            return 'secondary';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Orçamento - Built Organizer</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
</head>
<body>
    <div class="wrapper">
        <!-- Menu lateral -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php" class="active">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <li><a href="logout.php">SAIR</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <!-- Conteúdo principal -->
        <div class="content">
            <!-- Mensagens de alerta -->
            <?php if(isset($_SESSION['mensagem'])): ?>
                <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['mensagem']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
                <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
            <?php endif; ?>

            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-cash-coin"></i> Detalhes do Orçamento</h1>
                    <p>Informações detalhadas sobre o orçamento selecionado.</p>
                </div>
            </div>

            <div class="container mt-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Orçamento #<?php echo $orcamento['id']; ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Informações do Orçamento</h6>
                                <hr>
                                <p><strong>Descrição:</strong> <?php echo htmlspecialchars($orcamento['descricao']); ?></p>
                                <p><strong>Valor:</strong> €<?php echo number_format((float)$orcamento['valor'], 0, ',', '.'); ?></p>
                                <p><strong>Status:</strong>
                                    <span class="badge bg-<?php echo getStatusColor($orcamento['status']); ?>">
                                        <?php echo htmlspecialchars($orcamento['status']); ?>
                                    </span>
                                </p>
                                <p><strong>Data de Criação:</strong> <?php echo date('d/m/Y', strtotime($orcamento['data_criacao'])); ?></p>
                                <p><strong>Data de Aprovação:</strong>
                                    <?php if(isset($orcamento['data_aprovacao']) && $orcamento['data_aprovacao']): ?>
                                        <?php echo date('d/m/Y', strtotime($orcamento['data_aprovacao'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Pendente</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Informações da Obra</h6>
                                <hr>
                                <p><strong>Nome da Obra:</strong> <?php echo htmlspecialchars($orcamento['nome_obra']); ?></p>
                                <p><strong>Endereço:</strong> <?php echo htmlspecialchars($orcamento['endereço']); ?></p>
                                <p><strong>Data de Início:</strong> <?php echo date('d/m/Y', strtotime($orcamento['data_inicio'])); ?></p>
                                <p><strong>Data de Término Previsto:</strong> <?php echo date('d/m/Y', strtotime($orcamento['data_fim'])); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <a href="Projeto pag 3.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Voltar para Orçamentos
                        </a>

                        <button type="button" class="btn btn-warning" onclick="editarOrcamento(
                            '<?php echo $orcamento['id']; ?>',
                            '<?php echo $orcamento['obra_id']; ?>',
                            '<?php echo htmlspecialchars($orcamento['descricao']); ?>',
                            '<?php echo $orcamento['valor']; ?>',
                            '<?php echo htmlspecialchars($orcamento['status']); ?>'
                        )" data-bs-toggle="modal" data-bs-target="#editarOrcamentoModal">
                            <i class="bi bi-pencil"></i> Editar Orçamento
                        </button>

                        <?php if($orcamento['status'] == 'Em análise'): ?>
                            <button type="button" class="btn btn-success" onclick="aprovarOrcamento('<?php echo $orcamento['id']; ?>')">
                                <i class="bi bi-check-circle"></i> Aprovar Orçamento
                            </button>
                            <button type="button" class="btn btn-danger" onclick="rejeitarOrcamento('<?php echo $orcamento['id']; ?>')">
                                <i class="bi bi-x-circle"></i> Rejeitar Orçamento
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2024 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- Modal para Editar Orçamento -->
    <div class="modal fade" id="editarOrcamentoModal" tabindex="-1" aria-labelledby="editarOrcamentoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarOrcamentoModalLabel">Editar Orçamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarOrcamento" method="POST">
                        <input type="hidden" name="acao" value="editar">
                        <input type="hidden" id="id_orcamento_edit" name="id_orcamento">

                        <div class="mb-3">
                            <label for="obra_id_edit" class="form-label">Obra *</label>
                            <select class="form-select" id="obra_id_edit" name="obra_id" required>
                                <option value="">Selecione uma obra</option>
                                <?php
                                // Buscar todas as obras
                                $query_obras = "SELECT * FROM obras ORDER BY nome_obra";
                                $result_obras = mysqli_query($conn, $query_obras);

                                if ($result_obras && mysqli_num_rows($result_obras) > 0) {
                                    while ($obra = mysqli_fetch_assoc($result_obras)) {
                                        echo "<option value='" . $obra['obras_id'] . "'>" . htmlspecialchars($obra['nome_obra']) . "</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="descricao_edit" class="form-label">Descrição *</label>
                            <textarea class="form-control" id="descricao_edit" name="descricao" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="valor_edit" class="form-label">Valor (€) *</label>
                            <input type="number" step="0.01" class="form-control" id="valor_edit" name="valor" required>
                        </div>

                        <div class="mb-3">
                            <label for="status_edit" class="form-label">Status *</label>
                            <select class="form-select" id="status_edit" name="status" required>
                                <option value="Em análise">Em análise</option>
                                <option value="Aprovado">Aprovado</option>
                                <option value="Rejeitado">Rejeitado</option>
                                <option value="Concluído">Concluído</option>
                            </select>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS e dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Função para definir a cor do status
        function getStatusColor(status) {
            switch(status) {
                case 'Em análise':
                    return 'info';
                case 'Aprovado':
                    return 'success';
                case 'Rejeitado':
                    return 'danger';
                case 'Concluído':
                    return 'primary';
                default:
                    return 'secondary';
            }
        }

        // Função para editar orçamento
        function editarOrcamento(id, obra_id, descricao, valor, status) {
            document.getElementById('id_orcamento_edit').value = id;
            document.getElementById('obra_id_edit').value = obra_id;
            document.getElementById('descricao_edit').value = descricao;
            document.getElementById('valor_edit').value = valor;
            document.getElementById('status_edit').value = status;
        }

        // Função para aprovar orçamento
        function aprovarOrcamento(id) {
            if (confirm('Tem certeza que deseja aprovar este orçamento?')) {
                fetch('processar_orcamento.php?acao=aprovar&id=' + id)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Erro: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Ocorreu um erro ao processar a solicitação.');
                });
            }
        }

        // Função para rejeitar orçamento
        function rejeitarOrcamento(id) {
            if (confirm('Tem certeza que deseja rejeitar este orçamento?')) {
                fetch('processar_orcamento.php?acao=rejeitar&id=' + id)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Erro: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Ocorreu um erro ao processar a solicitação.');
                });
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Processar o formulário de editar orçamento via AJAX
            const formEditarOrcamento = document.getElementById('formEditarOrcamento');

            formEditarOrcamento.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(formEditarOrcamento);

                fetch('processar_orcamento.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editarOrcamentoModal'));
                        modal.hide();

                        // Exibir mensagem de sucesso
                        alert(data.message);

                        // Recarregar a página para mostrar as alterações
                        location.reload();
                    } else {
                        // Exibir mensagem de erro
                        alert('Erro: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Ocorreu um erro ao processar a solicitação.');
                });
            });
        });
    </script>
</body>
</html>


