<?php
require_once 'conexao.php';

// Verificar se o ID da obra foi fornecido
if (!isset($_GET['id_obra']) || empty($_GET['id_obra'])) {
    header('Content-Type: application/json');
    echo json_encode(['erro' => 'ID da obra não fornecido.']);
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Obter o ID da obra
$id_obra = mysqli_real_escape_string($conn, $_GET['id_obra']);

// Buscar dados da obra
$query = "SELECT * FROM obras WHERE obras_id = '$id_obra'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    $obra = mysqli_fetch_assoc($result);

    // Formatar datas para o formato HTML5 date input (YYYY-MM-DD)
    $obra['data_inicio'] = date('Y-m-d', strtotime($obra['data_inicio']));
    if ($obra['data_fim_prevista']) {
        $obra['data_fim_prevista'] = date('Y-m-d', strtotime($obra['data_fim_prevista']));
    }

    header('Content-Type: application/json');
    echo json_encode($obra);
} else {
    header('Content-Type: application/json');
    echo json_encode(['erro' => 'Obra não encontrada.']);
}

mysqli_close($conn);
?>





