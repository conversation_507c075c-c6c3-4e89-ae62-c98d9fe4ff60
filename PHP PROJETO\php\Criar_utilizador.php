<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';
require_once 'verificar_permissao.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário tem permissão (apenas administradores)
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')) {
    $_SESSION['AccessError'] = "Acesso negado. Apenas administradores podem criar usuários.";
    header("Location: ./Projeto.php");
    exit();
}

// Processar o formulário quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $mensagem = 'Erro de segurança. Por favor, tente novamente.';
        header("location: ./Registar_utilizador.php?msg=" . urlencode($mensagem));
        exit();
    }

    $conn = connectToDatabase();

    // Obter dados do formulário
    $username = mysqli_real_escape_string($conn, $_POST['nome_utilizador']);
    $email = mysqli_real_escape_string($conn, $_POST['email_utilizador']);
    $pass = mysqli_real_escape_string($conn, $_POST['password_utilizador']);
    $pass_confirma = mysqli_real_escape_string($conn, $_POST['password_utilizador_confirma']);
    $cargo = mysqli_real_escape_string($conn, $_POST['cargo_utilizador']); // Permitir seleção do cargo

    // Verificar se as senhas coincidem
    if($pass_confirma != $pass) {
        $mensagem = 'Password não corresponde com a confirmação! Tente novamente.';
        header("location: ./Registar_utilizador.php?msg=$mensagem");
        exit();
    }

    // Verificar se o nome de utilizador já existe usando prepared statement
    $check_query = "SELECT nome_utilizador FROM utilizadores WHERE nome_utilizador = ?";
    $check_stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($check_stmt, "s", $username);
    mysqli_stmt_execute($check_stmt);
    mysqli_stmt_store_result($check_stmt);

    if(mysqli_stmt_num_rows($check_stmt) > 0) {
        mysqli_stmt_close($check_stmt);
        $mensagem = 'Nome de utilizador já existe! Por favor escolha outro nome.';
        header("location: ./Registar_utilizador.php?msg=$mensagem");
        exit();
    }

    mysqli_stmt_close($check_stmt);

    // Hash da senha antes de armazenar usando a função de segurança
    $hashed_password = hashPassword($pass);

    // Criar a query para inserir o novo utilizador usando prepared statement
    $query = "INSERT INTO utilizadores (nome_utilizador, email_utilizador, password_utilizador, cargo_utilizador)
              VALUES (?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ssss", $username, $email, $hashed_password, $cargo);

    // Executar a query usando o prepared statement
    if(mysqli_stmt_execute($stmt)) {
        $mensagem = 'Utilizador criado com sucesso!';
        mysqli_stmt_close($stmt);
        mysqli_close($conn);

        // Armazenar mensagem na sessão para maior segurança
        $_SESSION['AdminSuccess'] = $mensagem;
        header("location: ./Projeto.php");
        exit();
    } else {
        $mensagem = 'Erro ao criar utilizador: ' . mysqli_stmt_error($stmt);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);

        // Usar urlencode para evitar problemas com caracteres especiais
        header("location: ./Registar_utilizador.php?msg=" . urlencode($mensagem));
        exit();
    }
}
?>
