<?php
session_start();
require_once 'security_functions.php';
require_once 'conexao.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        // Adicionar log para depuração
        error_log("Erro de token CSRF: Token recebido: " . ($_POST['csrf_token'] ?? 'não definido'));
        $_SESSION['mensagem'] = "Erro de segurança. Por favor, tente novamente.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }

    $conn = connectToDatabase();
    // Preparar e limpar os dados
    $nome_material = mysqli_real_escape_string($conn, $_POST['nome_material']);
    $quantidade = mysqli_real_escape_string($conn, $_POST['quantidade']);
    $unidade_medida = mysqli_real_escape_string($conn, $_POST['unidade_medida']);
    $obra_id = mysqli_real_escape_string($conn, $_POST['obra_id_material']);

    // Validar dados
    $erros = [];

    if (empty($nome_material)) {
        $erros[] = "O nome do material é obrigatório";
    }

    $quantidade_validada = validateNumber($quantidade, 0.01);
    if ($quantidade_validada === false) {
        $erros[] = "A quantidade deve ser um número positivo";
    } else {
        $quantidade = $quantidade_validada;
    }

    if (empty($obra_id)) {
        $erros[] = "É necessário selecionar uma obra";
    }

    // Verificar a estrutura da tabela materiais
    $query = "DESCRIBE materiais";
    $result = mysqli_query($conn, $query);
    $colunas = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $colunas[] = $row['Field'];
    }

    // Se não houver erros, inserir no banco de dados
    if (empty($erros)) {
        // Verificar se a coluna é 'obras_id' ou 'id_obra' ou outra
        if (in_array('obras_id', $colunas)) {
            $coluna_obra = 'obras_id';
        } elseif (in_array('id_obra', $colunas)) {
            $coluna_obra = 'id_obra';
        } elseif (in_array('obra_id', $colunas)) {
            $coluna_obra = 'obra_id';
        } else {
            // Se a coluna não existir, adicionar a coluna obras_id
            $query = "ALTER TABLE materiais ADD COLUMN obras_id INT NULL";
            mysqli_query($conn, $query);
            $coluna_obra = 'obras_id';
        }

        // Verificar se a coluna unidade_medida existe
        if (!in_array('unidade_medida', $colunas)) {
            $query = "ALTER TABLE materiais ADD COLUMN unidade_medida VARCHAR(20) DEFAULT 'unidades'";
            mysqli_query($conn, $query);
        }

        $query = "INSERT INTO materiais (nome, quantidade, unidade_medida, $coluna_obra) VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "sisi", $nome_material, $quantidade, $unidade_medida, $obra_id);

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['mensagem'] = "Material registrado com sucesso!";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao registrar material: " . mysqli_error($conn);
            $_SESSION['tipo_mensagem'] = "danger";
        }

        mysqli_stmt_close($stmt);
    } else {
        $_SESSION['mensagem'] = "Erro: " . implode(", ", $erros);
        $_SESSION['tipo_mensagem'] = "danger";
    }

    mysqli_close($conn);

    // Redirecionar explicitamente para a página de obras
    header("Location: Projeto pag 2.php");
    exit();
}
?>



