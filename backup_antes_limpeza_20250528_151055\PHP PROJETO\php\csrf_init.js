// Script para inicializar tokens CSRF em todos os formulários POST
document.addEventListener('DOMContentLoaded', function() {
    console.log("Script csrf_init.js carregado!");
    
    // Função para obter um token CSRF
    function obterTokenCSRF() {
        return fetch('get_csrf_token.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erro ao obter token CSRF: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data && data.token) {
                    return data.token;
                } else {
                    throw new Error('Token CSRF não encontrado na resposta');
                }
            });
    }
    
    // Função para adicionar token CSRF a um formulário
    function adicionarTokenCSRF(formulario, token) {
        // Verificar se o formulário já tem um token CSRF
        let csrfInput = formulario.querySelector('input[name="csrf_token"]');
        
        // Se não existir, criar um novo input para o token CSRF
        if (!csrfInput) {
            csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            
            // Adicionar o input como primeiro filho do formulário
            formulario.insertBefore(csrfInput, formulario.firstChild);
        }
        
        // Definir o valor do token
        csrfInput.value = token;
        
        return csrfInput;
    }
    
    // Obter um token CSRF
    obterTokenCSRF()
        .then(token => {
            console.log(`Token CSRF obtido: ${token.substring(0, 10)}...`);
            
            // Adicionar o token a todos os formulários POST
            document.querySelectorAll('form[method="POST"], form[method="post"]').forEach(formulario => {
                const csrfInput = adicionarTokenCSRF(formulario, token);
                console.log(`Token CSRF adicionado ao formulário ${formulario.id || 'sem id'}: ${csrfInput.value.substring(0, 10)}...`);
            });
        })
        .catch(error => {
            console.error("Erro ao inicializar tokens CSRF:", error);
        });
    
    // Adicionar event listener para o botão que abre o modal de adicionar obra
    const btnAdicionarObra = document.querySelector('button[data-bs-target="#adicionarObraModal"]');
    if (btnAdicionarObra) {
        btnAdicionarObra.addEventListener('click', function() {
            console.log("Botão de adicionar obra clicado!");
            
            // Obter um novo token CSRF para o formulário de adicionar obra
            obterTokenCSRF()
                .then(token => {
                    const formAdicionarObra = document.getElementById('formAdicionarObra');
                    if (formAdicionarObra) {
                        const csrfInput = adicionarTokenCSRF(formAdicionarObra, token);
                        console.log(`Token CSRF atualizado no formulário de adicionar obra: ${csrfInput.value.substring(0, 10)}...`);
                    }
                })
                .catch(error => {
                    console.error("Erro ao obter token CSRF para o formulário de adicionar obra:", error);
                });
        });
    }
});
