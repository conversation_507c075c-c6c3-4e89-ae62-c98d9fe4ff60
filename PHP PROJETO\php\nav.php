<?php
// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    return; // Não exibe o menu de navegação se o usuário não estiver logado
}

// Incluir o arquivo de verificação de permissões se ainda não foi incluído
if (!function_exists('checkMenuAccess')) {
    require_once 'verificar_permissao.php';
}
?>

<nav class="navbar">
    <div class="container">
        <ul class="nav-links">
            <li><a href="Projeto.php">INÍCIO</a></li>
            
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                <li><a href="Projeto pag 2.php">OBRAS</a></li>
            <?php endif; ?>
            
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
            <?php endif; ?>
            
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                <li><a href="Projeto pag 4.php">HORAS</a></li>
            <?php endif; ?>
            
            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                <li><a href="Projeto pag 5.php">GESTÃO</a></li>
            <?php endif; ?>
            
            <?php 
            // Verificação explícita para níveis 1 e 2
            if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] === '1' || $_SESSION['cargo_utilizador'] === '2')): 
            ?>
                <li><a href="Registar_utilizador.php">NOVO UTILIZADOR</a></li>
            <?php endif; ?>
            
            <li><a href="sobre_nos.php">SOBRE NÓS</a></li>
        </ul>
    </div>
</nav>