-- =====================================================
-- SCRIPT PRINCIPAL PARA CORRIGIR PROBLEMAS CRÍTICOS
-- =====================================================
-- Este script executa todas as correções necessárias
-- para resolver os problemas críticos identificados
-- SEM ALTERAR A FUNCIONALIDADE DA APLICAÇÃO

-- =====================================================
-- INSTRUÇÕES DE USO:
-- =====================================================
-- 1. Faça backup do banco de dados antes de executar
-- 2. Execute este script no MySQL/phpMyAdmin
-- 3. Verifique os resultados ao final
-- 4. Teste a aplicação após a execução

-- =====================================================
-- INÍCIO DAS CORREÇÕES
-- =====================================================

SELECT '=== INICIANDO CORREÇÕES CRÍTICAS ===' AS status;
SELECT NOW() AS inicio_execucao;

-- =====================================================
-- 1. BACKUP DE SEGURANÇA (ESTRUTURA)
-- =====================================================

SELECT '1. Criando backup de segurança...' AS etapa;

-- Criar tabela de log para acompanhar as alterações
CREATE TABLE IF NOT EXISTS `correcoes_log` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `etapa` VARCHAR(255),
    `status` VARCHAR(50),
    `detalhes` TEXT,
    `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Início das correções', 'INICIADO', 'Script de correções críticas iniciado');

-- =====================================================
-- 2. VERIFICAR ESTADO ATUAL DO BANCO
-- =====================================================

SELECT '2. Verificando estado atual do banco...' AS etapa;

-- Verificar tabelas existentes
SELECT 
    TABLE_NAME,
    ENGINE,
    TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('obras', 'utilizadores', 'registro_horas', 'orcamentos', 'materiais')
ORDER BY TABLE_NAME;

-- Log do estado atual
INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Verificação inicial', 'CONCLUÍDO', 
    CONCAT('Banco: ', DATABASE(), ' - Tabelas verificadas'));

-- =====================================================
-- 3. EXECUTAR CORREÇÃO DA ESTRUTURA PRINCIPAL
-- =====================================================

SELECT '3. Corrigindo estrutura principal do banco...' AS etapa;

-- Desabilitar verificação de foreign keys temporariamente
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- Criar/corrigir tabela obras
CREATE TABLE IF NOT EXISTS `obras` (
  `obras_id` INT NOT NULL AUTO_INCREMENT,
  `nome_obra` VARCHAR(255) NOT NULL,
  `endereço` VARCHAR(255) NOT NULL,
  `data_inicio` DATE NOT NULL,
  `data_fim` DATE NULL,
  `status` VARCHAR(50) DEFAULT 'Em andamento',
  `descricao` TEXT NULL,
  `orcamento` DECIMAL(15, 2) NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`obras_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Adicionar colunas que podem estar faltando
ALTER TABLE `obras` 
ADD COLUMN IF NOT EXISTS `status` VARCHAR(50) DEFAULT 'Em andamento',
ADD COLUMN IF NOT EXISTS `descricao` TEXT NULL,
ADD COLUMN IF NOT EXISTS `orcamento` DECIMAL(15, 2) NULL,
ADD COLUMN IF NOT EXISTS `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Criar/corrigir tabela utilizadores
CREATE TABLE IF NOT EXISTS `utilizadores` (
  `id_utilizadores` INT NOT NULL AUTO_INCREMENT,
  `nome_utilizador` VARCHAR(255) NOT NULL,
  `password` VARCHAR(255) NOT NULL,
  `cargo_utilizador` VARCHAR(50) NOT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_utilizadores`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Criar/corrigir tabela registro_horas
CREATE TABLE IF NOT EXISTS `registro_horas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `id_obra` INT NOT NULL,
  `id_usuario` INT NOT NULL,
  `horas` DECIMAL(5, 2) NOT NULL,
  `data_registro` DATE NOT NULL,
  `descricao` TEXT NULL,
  `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Criar/corrigir tabela orcamentos
CREATE TABLE IF NOT EXISTS `orcamentos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `obra_id` INT NOT NULL,
  `descricao` TEXT NOT NULL,
  `valor` DECIMAL(15, 2) NOT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `data_aprovacao` DATE NULL,
  `status` VARCHAR(50) DEFAULT 'Em análise',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Criar/corrigir tabela materiais
CREATE TABLE IF NOT EXISTS `materiais` (
  `material_id` INT NOT NULL AUTO_INCREMENT,
  `nome` VARCHAR(255) NOT NULL,
  `quantidade` DECIMAL(10, 2) NOT NULL,
  `unidade_medida` VARCHAR(50) DEFAULT 'unidades',
  `obras_id` INT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Estrutura principal', 'CONCLUÍDO', 'Tabelas principais criadas/corrigidas');

-- =====================================================
-- 4. LIMPAR DADOS ÓRFÃOS
-- =====================================================

SELECT '4. Limpando dados órfãos...' AS etapa;

-- Limpar registros de horas órfãos
DELETE FROM `registro_horas` 
WHERE `id_obra` NOT IN (SELECT `obras_id` FROM `obras`)
OR `id_usuario` NOT IN (SELECT `id_utilizadores` FROM `utilizadores`);

-- Limpar orçamentos órfãos
DELETE FROM `orcamentos` 
WHERE `obra_id` NOT IN (SELECT `obras_id` FROM `obras`);

-- Limpar materiais órfãos
UPDATE `materiais` 
SET `obras_id` = NULL 
WHERE `obras_id` IS NOT NULL 
AND `obras_id` NOT IN (SELECT `obras_id` FROM `obras`);

INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Limpeza de dados', 'CONCLUÍDO', 'Dados órfãos removidos');

-- =====================================================
-- 5. RECRIAR FOREIGN KEYS
-- =====================================================

SELECT '5. Recriando foreign keys...' AS etapa;

-- Remover foreign keys existentes que podem estar conflitantes
SET @sql = (SELECT GROUP_CONCAT(CONCAT('ALTER TABLE `', TABLE_NAME, '` DROP FOREIGN KEY `', CONSTRAINT_NAME, '`') SEPARATOR '; ')
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND TABLE_NAME IN ('registro_horas', 'orcamentos', 'materiais')
            AND CONSTRAINT_NAME != 'PRIMARY');

IF @sql IS NOT NULL THEN
    SET @sql = CONCAT(@sql, ';');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;

-- Recriar foreign keys corretas
ALTER TABLE `registro_horas` 
ADD CONSTRAINT `fk_registro_horas_obras` 
FOREIGN KEY (`id_obra`) REFERENCES `obras` (`obras_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `registro_horas` 
ADD CONSTRAINT `fk_registro_horas_utilizadores` 
FOREIGN KEY (`id_usuario`) REFERENCES `utilizadores` (`id_utilizadores`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `orcamentos` 
ADD CONSTRAINT `fk_orcamentos_obras` 
FOREIGN KEY (`obra_id`) REFERENCES `obras` (`obras_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `materiais` 
ADD CONSTRAINT `fk_materiais_obras` 
FOREIGN KEY (`obras_id`) REFERENCES `obras` (`obras_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Foreign keys', 'CONCLUÍDO', 'Foreign keys recriadas corretamente');

-- =====================================================
-- 6. CRIAR ÍNDICES PARA PERFORMANCE
-- =====================================================

SELECT '6. Criando índices para performance...' AS etapa;

-- Índices para obras
CREATE INDEX IF NOT EXISTS `idx_obras_status` ON `obras` (`status`);
CREATE INDEX IF NOT EXISTS `idx_obras_data_inicio` ON `obras` (`data_inicio`);

-- Índices para registro_horas
CREATE INDEX IF NOT EXISTS `idx_registro_obra` ON `registro_horas` (`id_obra`);
CREATE INDEX IF NOT EXISTS `idx_registro_usuario` ON `registro_horas` (`id_usuario`);
CREATE INDEX IF NOT EXISTS `idx_registro_data` ON `registro_horas` (`data_registro`);

-- Índices para orcamentos
CREATE INDEX IF NOT EXISTS `idx_orcamentos_obra` ON `orcamentos` (`obra_id`);
CREATE INDEX IF NOT EXISTS `idx_orcamentos_status` ON `orcamentos` (`status`);

-- Índices para materiais
CREATE INDEX IF NOT EXISTS `idx_materiais_obra` ON `materiais` (`obras_id`);

INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Índices', 'CONCLUÍDO', 'Índices de performance criados');

-- =====================================================
-- 7. VALIDAR DADOS
-- =====================================================

SELECT '7. Validando dados...' AS etapa;

-- Corrigir status nulos
UPDATE `obras` SET `status` = 'Em andamento' WHERE `status` IS NULL OR `status` = '';

-- Corrigir datas nulas
UPDATE `registro_horas` SET `data_registro` = CURDATE() WHERE `data_registro` IS NULL;

INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Validação de dados', 'CONCLUÍDO', 'Dados validados e corrigidos');

-- =====================================================
-- 8. REABILITAR VERIFICAÇÕES
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 9. RELATÓRIO FINAL
-- =====================================================

SELECT '=== CORREÇÕES CONCLUÍDAS ===' AS status;

-- Relatório das tabelas
SELECT 
    'TABELAS CORRIGIDAS' AS categoria,
    TABLE_NAME AS nome,
    TABLE_ROWS AS registros,
    ENGINE AS motor,
    TABLE_COLLATION AS collation
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('obras', 'utilizadores', 'registro_horas', 'orcamentos', 'materiais')
ORDER BY TABLE_NAME;

-- Relatório das foreign keys
SELECT 
    'FOREIGN KEYS' AS categoria,
    TABLE_NAME AS tabela,
    COLUMN_NAME AS coluna,
    CONSTRAINT_NAME AS constraint_nome,
    REFERENCED_TABLE_NAME AS tabela_referenciada,
    REFERENCED_COLUMN_NAME AS coluna_referenciada
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE CONSTRAINT_SCHEMA = DATABASE() 
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Log final
INSERT INTO `correcoes_log` (`etapa`, `status`, `detalhes`) 
VALUES ('Finalização', 'CONCLUÍDO', 'Todas as correções críticas foram aplicadas com sucesso');

-- Mostrar log completo
SELECT * FROM `correcoes_log` ORDER BY `timestamp`;

SELECT NOW() AS fim_execucao;
SELECT '=== SCRIPT CONCLUÍDO COM SUCESSO ===' AS resultado_final;

-- =====================================================
-- INSTRUÇÕES PÓS-EXECUÇÃO:
-- =====================================================
-- 1. Teste a aplicação web
-- 2. Verifique se todas as funcionalidades estão funcionando
-- 3. Se houver problemas, consulte a tabela 'correcoes_log'
-- 4. Mantenha o backup do banco original por segurança
-- =====================================================
