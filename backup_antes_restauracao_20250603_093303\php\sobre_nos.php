<?php
session_start();
require_once 'verificar_permissao.php';
?>
<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sobre Nós - Built Organizer</title>
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Ícones da biblioteca Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- JS da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
</head>
<body>
    <div class="wrapper">
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(isset($_SESSION['userName']) && $_SESSION['userName'] != null): ?>
                            <?php if(checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                                <li><a href="Projeto pag 2.php">OBRAS</a></li>
                            <?php endif; ?>

                            <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                                <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                            <?php endif; ?>

                            <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                                <li><a href="Projeto pag 4.php">HORAS</a></li>
                            <?php endif; ?>

                            <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                                <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                            <?php endif; ?>

                            <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                        <?php else: ?>
                            <li><a href="#" id="login-btn">LOGIN</a></li>
                        <?php endif; ?>

                        <li><a href="sobre_nos.php" class="active">SOBRE NÓS</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-building"></i> Sobre a Built Organizer</h1>
                    <p>Conheça a nossa empresa e a nossa missão de transformar a gestão de obras.</p>
                </div>
            </div>

            <div class="container py-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h2 class="h4 mb-0"><i class="bi bi-bullseye"></i> A nossa Missão</h2>
                            </div>
                            <div class="card-body">
                                <p>Fornecer soluções inovadoras em gestão de obras, simplificando os processos e maximizando os resultados para os nossos clientes.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h2 class="h4 mb-0"><i class="bi bi-eye"></i> A nossa Visão</h2>
                            </div>
                            <div class="card-body">
                                <p>Ser referência em sistemas de gestão para construção civil, proporcionando eficiência e qualidade em cada projeto.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h2 class="h4 mb-0"><i class="bi bi-tools"></i> Os nossos Serviços</h2>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-primary">
                                    <div class="card-body">
                                        <h3 class="h5 card-title"><i class="bi bi-building"></i> Gestão de Obras</h3>
                                        <p class="card-text">Controle completo de projetos e obras em andamento.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-success">
                                    <div class="card-body">
                                        <h3 class="h5 card-title"><i class="bi bi-cash-stack"></i> Orçamentos</h3>
                                        <p class="card-text">Ferramentas precisas para orçamentação e controle financeiro.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100 border-info">
                                    <div class="card-body">
                                        <h3 class="h5 card-title"><i class="bi bi-clock"></i> Gestão de Horas</h3>
                                        <p class="card-text">Controle eficiente de horas trabalhadas e produtividade.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h2 class="h4 mb-0"><i class="bi bi-people"></i> A nossa Equipa</h2>
                    </div>
                    <div class="card-body">
                        <p>Contamos com uma equipa de profissionais altamente qualificados, com experiência em desenvolvimento de software e conhecimento profundo do setor de construção civil.</p>
                        <p>A nossa equipa multidisciplinar trabalha em conjunto para oferecer soluções que realmente atendam às necessidades específicas de cada cliente.</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h2 class="h4 mb-0"><i class="bi bi-envelope"></i> Entre em Contato</h2>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item"><i class="bi bi-envelope me-2"></i> Email: <EMAIL></li>
                                    <li class="list-group-item"><i class="bi bi-telephone me-2"></i> Telefone:(351) 934663201</li>
                                    <li class="list-group-item"><i class="bi bi-geo-alt me-2"></i> Endereço: Rua Doutor Silvino Sequira Bairro Social Azinheira, Lote 18 - Rio Maior- 2040-068 </li>
                                </ul>
                                <div class="mt-3">
                                    <a href="https://wa.me/351934663201" target="_blank" class="btn btn-outline-success btn-lg"><i class="bi bi-whatsapp"></i> WhatsApp</a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Mapa estático do Google (não requer API key) -->
                                <div class="map-container" style="width: 100%; height: 300px; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                    <iframe
                                        width="100%"
                                        height="100%"
                                        frameborder="0"
                                        style="border:0"
                                        src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&q=Rio+Maior,Portugal"
                                        allowfullscreen>
                                    </iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2025 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- Script do Google Maps -->
    <script>
        function initMap() {
            // Coordenadas de Rio Maior, Portugal
            const rioMaior = { lat: 39.3369, lng: -8.9394 };

            // Criar o mapa
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 15,
                center: rioMaior,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true,
            });

            // Adicionar marcador
            const marker = new google.maps.Marker({
                position: rioMaior,
                map: map,
                title: "Built Organizer",
                animation: google.maps.Animation.DROP
            });

            // Adicionar janela de informação
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="padding: 10px; max-width: 200px;">
                        <h5 style="margin-top: 0;">Built Organizer</h5>
                        <p style="margin-bottom: 5px;">Rua Doutor Silvino Sequira</p>
                        <p style="margin-bottom: 5px;">Bairro Social Azinheira, Lote 18</p>
                        <p style="margin-bottom: 0;">Rio Maior, 2040-068</p>
                    </div>
                `
            });

            // Abrir a janela de informação quando clicar no marcador
            marker.addListener("click", () => {
                infoWindow.open(map, marker);
            });

            // Abrir a janela de informação por padrão
            infoWindow.open(map, marker);
        }
    </script>

    <!-- Carregar a API do Google Maps -->
    <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap" async defer></script>
</body>
</html>





