# Script para backup direcionado para pasta backup_copy no Desktop
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$desktopPath = [Environment]::GetFolderPath("Desktop")
$backupDestination = Join-Path $desktopPath "backup_copy"

Write-Host "=== BACKUP PARA DESKTOP/BACKUP_COPY ===" -ForegroundColor Green
Write-Host "Destino: $backupDestination" -ForegroundColor Cyan
Write-Host "Timestamp: $timestamp" -ForegroundColor Yellow

# Criar diretório de backup se não existir
if (!(Test-Path $backupDestination)) {
    New-Item -ItemType Directory -Path $backupDestination -Force | Out-Null
    Write-Host "Pasta backup_copy criada no Desktop" -ForegroundColor Green
} else {
    Write-Host "Pasta backup_copy já existe no Desktop" -ForegroundColor Yellow
}

# Criar subpasta com timestamp
$backupFolder = Join-Path $backupDestination "backup_aplicacao_$timestamp"
New-Item -ItemType Directory -Path $backupFolder -Force | Out-Null

Write-Host ""
Write-Host "Iniciando backup..." -ForegroundColor Yellow

# Função para mostrar progresso
function Show-Progress($activity, $status) {
    Write-Host "[$((Get-Date).ToString('HH:mm:ss'))] $activity - $status" -ForegroundColor Cyan
}

# 1. Backup da aplicação PHP
Show-Progress "APLICAÇÃO" "Copiando pasta PHP PROJETO..."
try {
    Copy-Item "PHP PROJETO" "$backupFolder\PHP PROJETO" -Recurse -Force
    Write-Host "✓ PHP PROJETO copiado com sucesso" -ForegroundColor Green
    $phpSuccess = $true
} catch {
    Write-Host "✗ Erro ao copiar PHP PROJETO: $($_.Exception.Message)" -ForegroundColor Red
    $phpSuccess = $false
}

# 2. Backup dos scripts
Show-Progress "SCRIPTS" "Copiando scripts PowerShell..."
try {
    Copy-Item "*.ps1" $backupFolder -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Scripts .ps1 copiados" -ForegroundColor Green
    $scriptsSuccess = $true
} catch {
    Write-Host "✗ Erro ao copiar scripts: $($_.Exception.Message)" -ForegroundColor Red
    $scriptsSuccess = $false
}

# 3. Backup da documentação
Show-Progress "DOCUMENTAÇÃO" "Copiando arquivos de documentação..."
try {
    Copy-Item "*.md" $backupFolder -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Documentação .md copiada" -ForegroundColor Green
    $docsSuccess = $true
} catch {
    Write-Host "✗ Erro ao copiar documentação: $($_.Exception.Message)" -ForegroundColor Red
    $docsSuccess = $false
}

# 4. Backup dos backups anteriores (se existirem)
Show-Progress "BACKUPS ANTERIORES" "Verificando backups existentes..."
$backupDirs = Get-ChildItem -Directory -Name "backup_*" | Where-Object { $_ -ne "backup_copy" }
if ($backupDirs.Count -gt 0) {
    $backupsAnterioresPath = Join-Path $backupFolder "backups_anteriores"
    New-Item -ItemType Directory -Path $backupsAnterioresPath -Force | Out-Null
    
    foreach ($dir in $backupDirs) {
        try {
            Copy-Item $dir "$backupsAnterioresPath\$dir" -Recurse -Force
            Write-Host "✓ Backup anterior incluído: $dir" -ForegroundColor Green
        } catch {
            Write-Host "⚠ Erro ao copiar backup anterior $dir" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "ℹ Nenhum backup anterior encontrado" -ForegroundColor Gray
}

# 5. Criar instruções específicas para backup do banco
Show-Progress "INSTRUÇÕES" "Criando instruções de backup do banco..."
$dbInstructions = @"
===============================================
INSTRUÇÕES PARA BACKUP DO BANCO DE DADOS
===============================================
Backup criado em: $(Get-Date)
Localização: $backupFolder

PASSO A PASSO - BACKUP DO BANCO:
===============================================

1. ABRIR phpMyAdmin
   - URL: http://localhost/phpmyadmin
   - Usuário: root
   - Senha: IEFP2025*

2. SELECIONAR O BANCO
   - Clique em "controle_obras" (lado esquerdo)

3. EXPORTAR DADOS
   - Clique na aba "Exportar" (topo)
   - Método: Personalizado
   - Tabelas: Selecionar todas
   - Formato: SQL
   - Estrutura: ✓ Adicionar CREATE TABLE
   - Dados: ✓ Adicionar INSERT
   - Clique "Executar"

4. SALVAR ARQUIVO
   - Nome: backup_controle_obras_$timestamp.sql
   - Salvar em: $backupFolder

VERIFICAÇÃO DO BACKUP:
===============================================
- Arquivo .sql deve ter > 50 KB
- Deve conter CREATE TABLE statements
- Deve conter INSERT statements (se houver dados)

TABELAS ESPERADAS:
===============================================
- obras (projetos de construção)
- utilizadores (usuários do sistema)  
- registro_horas (horas trabalhadas)
- orcamentos (orçamentos dos projetos)
- materiais (materiais dos projetos)

APÓS O BACKUP DO BANCO:
===============================================
1. Aplicar correções críticas:
   Execute: PHP PROJETO\SQL\EXECUTAR_CORRECOES_CRITICAS.sql

2. Testar aplicação:
   - Login/logout
   - Criar obra
   - Registrar horas
   - Criar orçamento

RESTAURAÇÃO (SE NECESSÁRIO):
===============================================
Arquivos: Copie PHP PROJETO de volta
Banco: Importe o arquivo .sql via phpMyAdmin

===============================================
"@

$dbInstructions | Out-File "$backupFolder\BACKUP_BANCO_INSTRUCOES.txt" -Encoding UTF8

# 6. Criar arquivo de informações do backup
Show-Progress "INFORMAÇÕES" "Criando arquivo de informações..."
$backupInfo = @"
===============================================
BACKUP COMPLETO DA APLICAÇÃO
===============================================

Data/Hora: $(Get-Date)
Localização: $backupFolder
Destino: Desktop\backup_copy\

CONTEÚDO DO BACKUP:
===============================================
$(if ($phpSuccess) { "✓" } else { "✗" }) PHP PROJETO/ (aplicação completa)
$(if ($scriptsSuccess) { "✓" } else { "✗" }) Scripts PowerShell (*.ps1)
$(if ($docsSuccess) { "✓" } else { "✗" }) Documentação (*.md)
$(if ($backupDirs.Count -gt 0) { "✓ Backups anteriores incluídos" } else { "ℹ Nenhum backup anterior" })
✓ Instruções de backup do banco
✓ Arquivo de informações

ESTRUTURA DO BACKUP:
===============================================
backup_aplicacao_$timestamp/
├── PHP PROJETO/
│   ├── php/ (arquivos da aplicação)
│   ├── SQL/ (scripts de correção)
│   └── CSS/ (estilos)
├── backups_anteriores/ (se existirem)
├── *.ps1 (scripts PowerShell)
├── *.md (documentação)
├── BACKUP_BANCO_INSTRUCOES.txt
└── README_BACKUP.txt (este arquivo)

PRÓXIMOS PASSOS:
===============================================
1. ⚠️  FAZER BACKUP DO BANCO DE DADOS
   - Siga BACKUP_BANCO_INSTRUCOES.txt
   - Salve o arquivo .sql nesta pasta

2. 🔧 APLICAR CORREÇÕES CRÍTICAS
   - Execute: PHP PROJETO\SQL\EXECUTAR_CORRECOES_CRITICAS.sql

3. 🧪 TESTAR APLICAÇÃO
   - Verificar todas as funcionalidades

4. 🛡️  MANTER BACKUP
   - Não delete até confirmar que tudo funciona

COMO RESTAURAR:
===============================================
1. Copiar PHP PROJETO de volta para o local original
2. Importar backup do banco via phpMyAdmin
3. Testar aplicação

SUPORTE:
===============================================
- Consulte README_CORRECOES_CRITICAS.md
- Verifique logs em: correcoes_log (após correções)
- Mantenha este backup por segurança

===============================================
BACKUP CRIADO COM SUCESSO!
===============================================
"@

$backupInfo | Out-File "$backupFolder\README_BACKUP.txt" -Encoding UTF8

# 7. Criar script de restauração rápida
Show-Progress "RESTAURAÇÃO" "Criando script de restauração..."
$restoreScript = @"
# Script de Restauração Rápida
# Execute este script para restaurar o backup

`$originalPath = "C:\Users\<USER>\OneDrive\Desktop\Projeto final"
`$backupPath = "$backupFolder"

Write-Host "=== RESTAURAÇÃO DO BACKUP ===" -ForegroundColor Yellow
Write-Host "De: `$backupPath" -ForegroundColor Cyan
Write-Host "Para: `$originalPath" -ForegroundColor Cyan

# Confirmar restauração
`$confirm = Read-Host "Deseja restaurar o backup? (s/N)"
if (`$confirm -match '^[sS]') {
    Write-Host "Restaurando arquivos..." -ForegroundColor Yellow
    
    # Restaurar PHP PROJETO
    if (Test-Path "`$backupPath\PHP PROJETO") {
        Copy-Item "`$backupPath\PHP PROJETO" "`$originalPath\PHP PROJETO" -Recurse -Force
        Write-Host "✓ PHP PROJETO restaurado" -ForegroundColor Green
    }
    
    # Restaurar scripts
    Copy-Item "`$backupPath\*.ps1" `$originalPath -Force -ErrorAction SilentlyContinue
    Copy-Item "`$backupPath\*.md" `$originalPath -Force -ErrorAction SilentlyContinue
    
    Write-Host "✓ Restauração concluída!" -ForegroundColor Green
    Write-Host "⚠️  Não esqueça de restaurar o banco de dados!" -ForegroundColor Yellow
} else {
    Write-Host "Restauração cancelada." -ForegroundColor Gray
}
"@

$restoreScript | Out-File "$backupFolder\RESTAURAR_BACKUP.ps1" -Encoding UTF8

# 8. Relatório final
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "           BACKUP CONCLUÍDO!" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "📁 LOCALIZAÇÃO:" -ForegroundColor Yellow
Write-Host "   $backupFolder" -ForegroundColor White
Write-Host ""
Write-Host "📊 ESTATÍSTICAS:" -ForegroundColor Yellow
Write-Host "   • Aplicação PHP: $(if ($phpSuccess) { "✓ Sucesso" } else { "✗ Falha" })" -ForegroundColor $(if ($phpSuccess) { "Green" } else { "Red" })
Write-Host "   • Scripts: $(if ($scriptsSuccess) { "✓ Sucesso" } else { "✗ Falha" })" -ForegroundColor $(if ($scriptsSuccess) { "Green" } else { "Red" })
Write-Host "   • Documentação: $(if ($docsSuccess) { "✓ Sucesso" } else { "✗ Falha" })" -ForegroundColor $(if ($docsSuccess) { "Green" } else { "Red" })
Write-Host "   • Backups anteriores: $($backupDirs.Count) incluídos" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 ARQUIVOS CRIADOS:" -ForegroundColor Yellow
Write-Host "   • README_BACKUP.txt (informações completas)" -ForegroundColor White
Write-Host "   • BACKUP_BANCO_INSTRUCOES.txt (como fazer backup do banco)" -ForegroundColor White
Write-Host "   • RESTAURAR_BACKUP.ps1 (script de restauração)" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  PRÓXIMO PASSO CRÍTICO:" -ForegroundColor Red
Write-Host "   FAZER BACKUP DO BANCO DE DADOS!" -ForegroundColor Red
Write-Host "   Siga as instruções em: BACKUP_BANCO_INSTRUCOES.txt" -ForegroundColor Yellow
Write-Host ""
Write-Host "🎯 APÓS BACKUP DO BANCO:" -ForegroundColor Yellow
Write-Host "   1. Aplicar correções críticas (SQL)" -ForegroundColor White
Write-Host "   2. Testar aplicação" -ForegroundColor White
Write-Host "   3. Manter backup por segurança" -ForegroundColor White
Write-Host ""
Write-Host "🎉 Backup criado com sucesso no Desktop!" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
