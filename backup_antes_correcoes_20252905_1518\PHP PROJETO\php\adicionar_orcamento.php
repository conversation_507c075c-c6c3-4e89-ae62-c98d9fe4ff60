<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';
require_once 'verificar_permissao.php'; // Corrigido: menu_access.php -> verificar_permissao.php

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Verificar se o usuário tem permissão para acessar esta página
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')) {
    $_SESSION['AccessError'] = "Você não tem permissão para acessar a página de orçamentos.";
    header("Location: Projeto.php");
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['mensagem'] = "Erro de segurança. Por favor, tente novamente.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 3.php");
        exit();
    }

    $obra_id = isset($_POST['obra_id']) ? $_POST['obra_id'] : '';
    $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : '';
    $valor = isset($_POST['valor']) ? str_replace(',', '.', $_POST['valor']) : '';
    $status = isset($_POST['status']) ? $_POST['status'] : 'Em análise';
    
    // Validar dados
    $erros = [];
    
    if (empty($obra_id)) {
        $erros[] = "É necessário selecionar uma obra";
    }
    
    if (empty($descricao)) {
        $erros[] = "A descrição é obrigatória";
    }
    
    if (!is_numeric($valor) || $valor <= 0) {
        $erros[] = "O valor deve ser um número positivo";
    }
    
    if (empty($status)) {
        $erros[] = "O status é obrigatório";
    }
    
    if (empty($erros)) {
        // Inserir orçamento
        $query = "INSERT INTO orcamentos (obra_id, descricao, valor, status) VALUES (?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $query);
        
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "isds", $obra_id, $descricao, $valor, $status);
            
            if (mysqli_stmt_execute($stmt)) {
                $_SESSION['mensagem'] = "Orçamento adicionado com sucesso!";
                $_SESSION['tipo_mensagem'] = "success";
                header("Location: Projeto pag 3.php");
                exit();
            } else {
                $_SESSION['mensagem'] = "Erro ao adicionar orçamento: " . mysqli_error($conn);
                $_SESSION['tipo_mensagem'] = "danger";
            }
            
            mysqli_stmt_close($stmt);
        } else {
            $_SESSION['mensagem'] = "Erro ao preparar a consulta: " . mysqli_error($conn);
            $_SESSION['tipo_mensagem'] = "danger";
        }
    } else {
        $_SESSION['mensagem'] = implode("<br>", $erros);
        $_SESSION['tipo_mensagem'] = "danger";
    }
}

// Buscar todas as obras para o formulário
$query_obras = "SELECT obras_id, nome_obra FROM obras ORDER BY nome_obra";
$result_obras = mysqli_query($conn, $query_obras);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adicionar Novo Orçamento - Built Organizer</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Ícones da biblioteca Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php" class="active">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-cash-coin"></i> Adicionar Novo Orçamento</h1>
                    <p>Preencha o formulário abaixo para adicionar um novo orçamento ao sistema.</p>
                </div>
            </div>

            <div class="container mt-4">
                <?php if(isset($_SESSION['mensagem'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['mensagem']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    </div>
                    <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="POST" action="adicionar_orcamento.php">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="obra_id" class="form-label">Obra *</label>
                                <select class="form-select" id="obra_id" name="obra_id" required>
                                    <option value="">Selecione uma obra</option>
                                    <?php while ($obra = mysqli_fetch_assoc($result_obras)): ?>
                                        <option value="<?php echo $obra['obras_id']; ?>"><?php echo htmlspecialchars($obra['nome_obra']); ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="descricao" class="form-label">Descrição *</label>
                                <textarea class="form-control" id="descricao" name="descricao" rows="3" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="valor" class="form-label">Valor (€) *</label>
                                <input type="number" step="0.01" class="form-control" id="valor" name="valor" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Status *</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="Em análise" selected>Em análise</option>
                                    <option value="Aprovado">Aprovado</option>
                                    <option value="Rejeitado">Rejeitado</option>
                                    <option value="Concluído">Concluído</option>
                                </select>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="Projeto pag 3.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Voltar
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Salvar Orçamento
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2025 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>
</body>
</html>
