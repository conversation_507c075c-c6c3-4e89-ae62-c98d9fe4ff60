<?php
# Mostra mensagem de alerta vinda do carregamento na BD
if(isset($_GET['msg'])){
    echo "<script>alert('" . $_GET['msg'] . "');</script>";
}
?>

<!DOCTYPE html>
<html lang="pt-pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Built Organizer</title>

        <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

        <!-- Bootstrap CSS e JS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

        <!-- Scripts personalizados -->
        <script>
            // Função global para fechar modais
            function closeModal(modalId) {
                const modalElement = document.getElementById(modalId);
                if (modalElement) {
                    const modalInstance = bootstrap.Modal.getInstance(modalElement);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            }
        </script>

        <link rel="stylesheet" href="projeto.css">
        <link rel="stylesheet" href="homepage.css">

    </head>


    <body>
        <header class="cabecalho">
            <center>
                <div class="row">
                    <div class="col-sm-12 col-md-3">
						<a href="../PHP PROJETO/Projeto.php">
                        	<img src="../PHP PROJETO/Imagem1.png" alt="" class="logotipo">
						</a>
                    </div>

                    <div class="col-sm-12 col-md-6">

                        <p class="titulo">
                            Built Organizer
                        </p>

                    </div>

                    <div class="col-sm-12 col-md-3">

						<?php if (isset($_SESSION['userName'])){
									echo ("<a href='./edita_utilizador.php' class='login'><img src='../img/users/".($_SESSION['photo'])."' class='imagem_login'/></a>");
									echo ("<a href='./logout.php' class='logout'><img src='../img/logout.png' alt=''></a>");
									echo ("<br>");
									echo ("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									Bem vindo ". $_SESSION['userName']);
								}else{
									echo ("<a href='./Login.php' title='Aceder à àrea reservada' class='login'>
												<img src='../Img/login.png' alt=''>
											</a>");
								}
						?>

                    </div>
                </div>
            </center>
        </header>

		<?php if (isset($_SESSION['userName']) != null){
			include "nav.php";
		}
		?>
