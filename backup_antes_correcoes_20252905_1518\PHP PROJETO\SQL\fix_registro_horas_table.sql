-- =====================================================
-- SCRIPT PARA CORRIGIR TABELA DE REGISTRO DE HORAS
-- =====================================================
-- Este script padroniza a tabela de registro de horas
-- resolvendo inconsistências entre 'registos_horas' e 'registro_horas'

-- =====================================================
-- 1. VERIFICAR TABELAS EXISTENTES
-- =====================================================

-- Verificar se existe tabela 'registos_horas' (português)
SET @registos_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'registos_horas'
);

-- Verificar se existe tabela 'registro_horas' (brasileiro)
SET @registro_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'registro_horas'
);

-- =====================================================
-- 2. CRIAR TABELA PADRÃO SE NÃO EXISTIR
-- =====================================================

-- Criar tabela registro_horas com estrutura padrão
CREATE TABLE IF NOT EXISTS `registro_horas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `id_obra` INT NOT NULL,
  `id_usuario` INT NOT NULL,
  `horas` DECIMAL(5, 2) NOT NULL,
  `data_registro` DATE NOT NULL,
  `descricao` TEXT NULL,
  `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_registro_obra` (`id_obra`),
  INDEX `idx_registro_usuario` (`id_usuario`),
  INDEX `idx_registro_data` (`data_registro`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 3. MIGRAR DADOS SE NECESSÁRIO
-- =====================================================

-- Se existir 'registos_horas' mas não 'registro_horas', migrar dados
SET @sql = IF(@registos_exists > 0 AND @registro_exists = 0,
    'INSERT INTO registro_horas (id_obra, id_usuario, horas, data_registro, descricao)
     SELECT 
         COALESCE(id_obra, 0) as id_obra,
         COALESCE(id_utilizador, id_usuario, 0) as id_usuario,
         COALESCE(horas, 0) as horas,
         COALESCE(data_registo, data_registro, CURDATE()) as data_registro,
         COALESCE(descricao, "") as descricao
     FROM registos_horas
     WHERE NOT EXISTS (
         SELECT 1 FROM registro_horas rh 
         WHERE rh.id_obra = registos_horas.id_obra 
         AND rh.id_usuario = COALESCE(registos_horas.id_utilizador, registos_horas.id_usuario)
         AND rh.data_registro = COALESCE(registos_horas.data_registo, registos_horas.data_registro)
     )',
    'SELECT "Migração não necessária" as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. PADRONIZAR ESTRUTURA DA TABELA
-- =====================================================

-- Garantir que todos os campos necessários existam
ALTER TABLE `registro_horas`
ADD COLUMN IF NOT EXISTS `id` INT NOT NULL AUTO_INCREMENT FIRST,
ADD COLUMN IF NOT EXISTS `id_obra` INT NOT NULL AFTER `id`,
ADD COLUMN IF NOT EXISTS `id_usuario` INT NOT NULL AFTER `id_obra`,
ADD COLUMN IF NOT EXISTS `horas` DECIMAL(5, 2) NOT NULL AFTER `id_usuario`,
ADD COLUMN IF NOT EXISTS `data_registro` DATE NOT NULL AFTER `horas`,
ADD COLUMN IF NOT EXISTS `descricao` TEXT NULL AFTER `data_registro`,
ADD COLUMN IF NOT EXISTS `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER `descricao`;

-- Garantir que a chave primária esteja correta
ALTER TABLE `registro_horas` 
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`);

-- Garantir que o campo id seja auto_increment
ALTER TABLE `registro_horas` 
MODIFY COLUMN `id` INT NOT NULL AUTO_INCREMENT;

-- =====================================================
-- 5. LIMPAR DADOS INCONSISTENTES
-- =====================================================

-- Corrigir valores nulos ou inválidos
UPDATE `registro_horas` 
SET `id_obra` = 0 
WHERE `id_obra` IS NULL OR `id_obra` = 0;

UPDATE `registro_horas` 
SET `id_usuario` = 0 
WHERE `id_usuario` IS NULL OR `id_usuario` = 0;

UPDATE `registro_horas` 
SET `horas` = 0 
WHERE `horas` IS NULL OR `horas` < 0;

UPDATE `registro_horas` 
SET `data_registro` = CURDATE() 
WHERE `data_registro` IS NULL;

UPDATE `registro_horas` 
SET `descricao` = '' 
WHERE `descricao` IS NULL;

-- Remover registros com dados inválidos que não podem ser corrigidos
DELETE FROM `registro_horas` 
WHERE `id_obra` = 0 OR `id_usuario` = 0;

-- =====================================================
-- 6. CRIAR ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Remover índices existentes que podem estar duplicados
DROP INDEX IF EXISTS `idx_registro_obra` ON `registro_horas`;
DROP INDEX IF EXISTS `idx_registro_usuario` ON `registro_horas`;
DROP INDEX IF EXISTS `idx_registro_data` ON `registro_horas`;
DROP INDEX IF EXISTS `idx_data_registro` ON `registro_horas`;

-- Criar índices otimizados
CREATE INDEX `idx_registro_obra` ON `registro_horas` (`id_obra`);
CREATE INDEX `idx_registro_usuario` ON `registro_horas` (`id_usuario`);
CREATE INDEX `idx_registro_data` ON `registro_horas` (`data_registro`);
CREATE INDEX `idx_registro_composto` ON `registro_horas` (`id_obra`, `id_usuario`, `data_registro`);

-- =====================================================
-- 7. CRIAR VIEW DE COMPATIBILIDADE
-- =====================================================

-- View para compatibilidade com código que usa nomes diferentes
CREATE OR REPLACE VIEW `registos_horas_view` AS
SELECT 
    `id`,
    `id` AS `id_registro`,
    `id_obra`,
    `id_usuario`,
    `id_usuario` AS `id_utilizador`,
    `horas`,
    `data_registro`,
    `data_registro` AS `data_registo`,
    `descricao`,
    `criado_em`,
    `criado_em` AS `timestamp`
FROM `registro_horas`;

-- =====================================================
-- 8. VALIDAR ESTRUTURA FINAL
-- =====================================================

-- Verificar estrutura da tabela
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    EXTRA
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'registro_horas'
ORDER BY ORDINAL_POSITION;

-- Verificar índices
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'registro_horas'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- 9. REMOVER TABELA ANTIGA SE MIGRAÇÃO FOI FEITA
-- =====================================================

-- Comentado por segurança - descomente apenas se tiver certeza
-- SET @drop_sql = IF(@registos_exists > 0 AND @registro_exists > 0,
--     'DROP TABLE IF EXISTS registos_horas',
--     'SELECT "Tabela registos_horas não removida" as status'
-- );
-- 
-- PREPARE stmt FROM @drop_sql;
-- EXECUTE stmt;
-- DEALLOCATE PREPARE stmt;

-- =====================================================
-- SCRIPT CONCLUÍDO
-- =====================================================
-- Tabela registro_horas padronizada
-- Dados migrados se necessário
-- Índices otimizados criados
-- View de compatibilidade disponível
-- =====================================================
