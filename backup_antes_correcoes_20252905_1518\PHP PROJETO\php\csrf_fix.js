// Script para corrigir problemas com tokens CSRF
document.addEventListener('DOMContentLoaded', function() {
    console.log("Script csrf_fix.js carregado!");

    // Verificar o formulário de adicionar obra
    const formAdicionarObra = document.getElementById('formAdicionarObra');
    if (formAdicionarObra) {
        console.log("Formulário de adicionar obra encontrado!");

        // Verificar se o token CSRF já existe
        let csrfInput = formAdicionarObra.querySelector('input[name="csrf_token"]');

        if (!csrfInput || !csrfInput.value) {
            console.log("Token CSRF não encontrado ou vazio no formulário de adicionar obra. Adicionando novo token...");

            // Se não existir, criar um novo input para o token CSRF
            if (!csrfInput) {
                csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';

                // Adicionar o input como primeiro filho do formulário
                formAdicionarObra.insertBefore(csrfInput, formAdicionarObra.firstChild);
            }

            // Obter um novo token CSRF via fetch
            fetch('get_csrf_token.php')
                .then(response => response.json())
                .then(data => {
                    if (data.token) {
                        csrfInput.value = data.token;
                        console.log(`Novo token CSRF adicionado ao formulário de adicionar obra: ${data.token.substring(0, 10)}...`);
                    } else {
                        console.error("Não foi possível obter um token CSRF para o formulário de adicionar obra.");
                    }
                })
                .catch(error => {
                    console.error("Erro ao obter token CSRF para o formulário de adicionar obra:", error);
                });
        } else {
            console.log(`Token CSRF já existe no formulário de adicionar obra: ${csrfInput.value.substring(0, 10)}...`);
        }

        // Adicionar um event listener para o envio do formulário
        formAdicionarObra.addEventListener('submit', function(e) {
            console.log("Formulário de adicionar obra sendo enviado!");

            // Verificar se o token CSRF está presente e não vazio
            csrfInput = formAdicionarObra.querySelector('input[name="csrf_token"]');

            if (!csrfInput || !csrfInput.value) {
                console.error("Token CSRF não encontrado ou vazio no momento do envio do formulário de adicionar obra!");
                e.preventDefault();

                // Alertar o usuário
                alert("Erro de segurança: Token CSRF não encontrado. A página será recarregada para corrigir o problema.");

                // Recarregar a página
                location.reload();
                return false;
            }

            console.log(`Formulário de adicionar obra sendo enviado com token CSRF: ${csrfInput.value.substring(0, 10)}...`);
        });
    } else {
        console.warn("Formulário de adicionar obra não encontrado!");
    }

    // Verificar especificamente o formulário de edição de obras
    const formEditarObra = document.getElementById('formEditarObra');
    if (formEditarObra) {
        console.log("Formulário de edição de obras encontrado!");

        // Verificar se o token CSRF já existe
        let csrfInput = formEditarObra.querySelector('input[name="csrf_token"]');

        if (!csrfInput || !csrfInput.value) {
            console.log("Token CSRF não encontrado ou vazio. Adicionando novo token...");

            // Se não existir, criar um novo input para o token CSRF
            if (!csrfInput) {
                csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';

                // Adicionar o input como primeiro filho do formulário
                formEditarObra.insertBefore(csrfInput, formEditarObra.firstChild);
            }

            // Obter um novo token CSRF via fetch
            fetch('get_csrf_token.php')
                .then(response => response.json())
                .then(data => {
                    if (data.token) {
                        csrfInput.value = data.token;
                        console.log(`Novo token CSRF adicionado: ${data.token.substring(0, 10)}...`);
                    } else {
                        console.error("Não foi possível obter um token CSRF.");
                    }
                })
                .catch(error => {
                    console.error("Erro ao obter token CSRF:", error);
                });
        } else {
            console.log(`Token CSRF já existe: ${csrfInput.value.substring(0, 10)}...`);
        }

        // Adicionar um event listener para o envio do formulário
        formEditarObra.addEventListener('submit', function(e) {
            console.log("Formulário de edição de obras sendo enviado!");

            // Verificar se o token CSRF está presente e não vazio
            csrfInput = formEditarObra.querySelector('input[name="csrf_token"]');

            if (!csrfInput || !csrfInput.value) {
                console.error("Token CSRF não encontrado ou vazio no momento do envio!");
                e.preventDefault();

                // Alertar o usuário
                alert("Erro de segurança: Token CSRF não encontrado. A página será recarregada para corrigir o problema.");

                // Recarregar a página
                location.reload();
                return false;
            }

            console.log(`Formulário sendo enviado com token CSRF: ${csrfInput.value.substring(0, 10)}...`);
        });
    } else {
        console.warn("Formulário de edição de obras não encontrado!");
    }

    // Verificar o modal personalizado de edição de obras
    const modalEditarObra = document.getElementById('editarObraModalCustom');
    if (modalEditarObra) {
        console.log("Modal personalizado de edição de obras encontrado!");

        // Adicionar um observer para detectar quando o modal é exibido
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'style' &&
                    modalEditarObra.style.display === 'block') {
                    console.log("Modal de edição de obras exibido!");

                    // Verificar e corrigir o token CSRF quando o modal é exibido
                    const formNoModal = modalEditarObra.querySelector('form');
                    if (formNoModal) {
                        let csrfInput = formNoModal.querySelector('input[name="csrf_token"]');

                        if (!csrfInput || !csrfInput.value) {
                            console.log("Token CSRF não encontrado ou vazio no modal. Adicionando novo token...");

                            // Se não existir, criar um novo input para o token CSRF
                            if (!csrfInput) {
                                csrfInput = document.createElement('input');
                                csrfInput.type = 'hidden';
                                csrfInput.name = 'csrf_token';

                                // Adicionar o input como primeiro filho do formulário
                                formNoModal.insertBefore(csrfInput, formNoModal.firstChild);
                            }

                            // Obter um novo token CSRF via fetch
                            fetch('get_csrf_token.php')
                                .then(response => response.json())
                                .then(data => {
                                    if (data.token) {
                                        csrfInput.value = data.token;
                                        console.log(`Novo token CSRF adicionado ao modal: ${data.token.substring(0, 10)}...`);
                                    } else {
                                        console.error("Não foi possível obter um token CSRF para o modal.");
                                    }
                                })
                                .catch(error => {
                                    console.error("Erro ao obter token CSRF para o modal:", error);
                                });
                        } else {
                            console.log(`Token CSRF já existe no modal: ${csrfInput.value.substring(0, 10)}...`);
                        }
                    }
                }
            });
        });

        observer.observe(modalEditarObra, { attributes: true });
    } else {
        console.warn("Modal personalizado de edição de obras não encontrado!");
    }

    // Verificar o modal de adicionar obra
    const modalAdicionarObra = document.getElementById('adicionarObraModal');
    if (modalAdicionarObra) {
        console.log("Modal de adicionar obra encontrado!");

        // Adicionar um observer para detectar quando o modal é exibido
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class' &&
                    modalAdicionarObra.classList.contains('show')) {
                    console.log("Modal de adicionar obra exibido!");

                    // Verificar e corrigir o token CSRF quando o modal é exibido
                    const formNoModal = modalAdicionarObra.querySelector('form');
                    if (formNoModal) {
                        let csrfInput = formNoModal.querySelector('input[name="csrf_token"]');

                        if (!csrfInput || !csrfInput.value) {
                            console.log("Token CSRF não encontrado ou vazio no modal de adicionar obra. Adicionando novo token...");

                            // Se não existir, criar um novo input para o token CSRF
                            if (!csrfInput) {
                                csrfInput = document.createElement('input');
                                csrfInput.type = 'hidden';
                                csrfInput.name = 'csrf_token';

                                // Adicionar o input como primeiro filho do formulário
                                formNoModal.insertBefore(csrfInput, formNoModal.firstChild);
                            }

                            // Obter um novo token CSRF via fetch
                            fetch('get_csrf_token.php')
                                .then(response => response.json())
                                .then(data => {
                                    if (data.token) {
                                        csrfInput.value = data.token;
                                        console.log(`Novo token CSRF adicionado ao modal de adicionar obra: ${data.token.substring(0, 10)}...`);
                                    } else {
                                        console.error("Não foi possível obter um token CSRF para o modal de adicionar obra.");
                                    }
                                })
                                .catch(error => {
                                    console.error("Erro ao obter token CSRF para o modal de adicionar obra:", error);
                                });
                        } else {
                            console.log(`Token CSRF já existe no modal de adicionar obra: ${csrfInput.value.substring(0, 10)}...`);
                        }
                    }
                }
            });
        });

        observer.observe(modalAdicionarObra, { attributes: true });
    } else {
        console.warn("Modal de adicionar obra não encontrado!");
    }
});
