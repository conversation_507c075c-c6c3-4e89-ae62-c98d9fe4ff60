-- Adicionar restrições de chave estrangeira
ALTER TABLE obras
ADD CONSTRAINT fk_obras_responsavel
FOREIGN KEY (id_responsavel) REFERENCES utilizadores(id_utilizadores);

ALTER TABLE registro_horas
ADD CONSTRAINT fk_registro_obra
FOREIGN KEY (id_obra) REFERENCES obras(id_obra),
ADD CONSTRAINT fk_registro_usuario
FOREIGN KEY (id_usuario) REFERENCES utilizadores(id_utilizadores);

-- Adicionar índices para melhor performance
ALTER TABLE obras
ADD INDEX idx_status (status),
ADD INDEX idx_data_inicio (data_inicio),
ADD INDEX idx_responsavel (id_responsavel);

ALTER TABLE registro_horas
ADD INDEX idx_data_registro (data_registro);

-- Adicionar campo para última atualização
ALTER TABLE obras
ADD COLUMN ultima_atualizacao TIMESTAMP 
DEFAULT CURRENT_TIMESTAMP 
ON UPDATE CURRENT_TIMESTAMP;

ALTER TABLE utilizadores
ADD COLUMN ultima_atualizacao TIMESTAMP 
DEFAULT CURRENT_TIMESTAMP 
ON UPDATE CURRENT_TIMESTAMP;

-- Adicionar restrição para status
ALTER TABLE obras
ADD CONSTRAINT chk_status 
CHECK (status IN ('Em andamento', 'Concluída', 'Pausada', 'Cancelada'));