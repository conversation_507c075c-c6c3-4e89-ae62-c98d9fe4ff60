# 🔧 Correções Críticas do Banco de Dados

## 📋 Resumo

Este conjunto de scripts corrige os **problemas críticos** identificados na aplicação sem alterar o código ou funcionalidades existentes.

## 🚨 PROBLEMAS CRÍTICOS CORRIGIDOS

### 1. **Inconsistências na Estrutura de Banco de Dados**
- ✅ Padronização da tabela `obras` (obras_id vs id_obra)
- ✅ Unificação de campos (endereço vs localizacao vs endereco)
- ✅ Padronização de campos de data (data_fim vs data_fim_prevista vs prazo)

### 2. **Foreign Keys Inconsistentes**
- ✅ Correção de referências conflitantes
- ✅ Limpeza de dados órfãos
- ✅ Recriação de constraints corretas

### 3. **Tabela de Registro de Horas com Múltiplas Definições**
- ✅ Unificação entre `registos_horas` e `registro_horas`
- ✅ Migração segura de dados
- ✅ Padronização de estrutura

## 📁 Arquivos Incluídos

### 🎯 **EXECUTAR_CORRECOES_CRITICAS.sql** (PRINCIPAL)
- Script principal que executa todas as correções
- **EXECUTE ESTE PRIMEIRO**
- Inclui logging e validação

### 🔧 Scripts de Apoio:
- `fix_database_structure.sql` - Padronização da estrutura
- `fix_foreign_keys.sql` - Correção de foreign keys
- `fix_registro_horas_table.sql` - Correção da tabela de horas

## 🚀 Como Aplicar as Correções

### ⚠️ **ANTES DE COMEÇAR - BACKUP OBRIGATÓRIO**

```sql
-- 1. Criar backup completo do banco
mysqldump -u root -p controle_obras > backup_antes_correcoes.sql

-- 2. Ou via phpMyAdmin: Exportar > SQL > Executar
```

### 📝 **Passo a Passo**

#### **Opção 1: Via phpMyAdmin (Recomendado)**

1. **Acesse phpMyAdmin**
2. **Selecione o banco `controle_obras`**
3. **Vá para a aba "SQL"**
4. **Copie e cole o conteúdo de `EXECUTAR_CORRECOES_CRITICAS.sql`**
5. **Clique em "Executar"**
6. **Aguarde a conclusão (pode demorar alguns minutos)**

#### **Opção 2: Via Linha de Comando**

```bash
# Navegar até a pasta SQL
cd "PHP PROJETO/SQL"

# Executar o script principal
mysql -u root -p controle_obras < EXECUTAR_CORRECOES_CRITICAS.sql
```

### ✅ **Verificação Pós-Execução**

1. **Verificar se o script executou sem erros**
2. **Consultar a tabela de log:**
   ```sql
   SELECT * FROM correcoes_log ORDER BY timestamp;
   ```
3. **Testar a aplicação web:**
   - Login/Logout
   - Criar/Editar obras
   - Registrar horas
   - Criar orçamentos
   - Gerenciar materiais

## 📊 O Que Foi Corrigido

### **Estrutura Padronizada:**

#### **Tabela `obras`:**
```sql
obras_id (INT, AUTO_INCREMENT, PRIMARY KEY)
nome_obra (VARCHAR(255), NOT NULL)
endereço (VARCHAR(255), NOT NULL)
data_inicio (DATE, NOT NULL)
data_fim (DATE, NULL)
status (VARCHAR(50), DEFAULT 'Em andamento')
descricao (TEXT, NULL)
orcamento (DECIMAL(15,2), NULL)
data_criacao (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
```

#### **Tabela `registro_horas`:**
```sql
id (INT, AUTO_INCREMENT, PRIMARY KEY)
id_obra (INT, NOT NULL, FK -> obras.obras_id)
id_usuario (INT, NOT NULL, FK -> utilizadores.id_utilizadores)
horas (DECIMAL(5,2), NOT NULL)
data_registro (DATE, NOT NULL)
descricao (TEXT, NULL)
criado_em (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
```

#### **Foreign Keys Corrigidas:**
- `registro_horas.id_obra` → `obras.obras_id`
- `registro_horas.id_usuario` → `utilizadores.id_utilizadores`
- `orcamentos.obra_id` → `obras.obras_id`
- `materiais.obras_id` → `obras.obras_id`

### **Índices Adicionados:**
- Performance otimizada para consultas frequentes
- Índices em campos de busca e relacionamento

## 🔍 Monitoramento

### **Tabela de Log Criada:**
```sql
SELECT * FROM correcoes_log;
```

### **Verificar Integridade:**
```sql
-- Verificar foreign keys
SELECT 
    TABLE_NAME, 
    COLUMN_NAME, 
    CONSTRAINT_NAME, 
    REFERENCED_TABLE_NAME 
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE CONSTRAINT_SCHEMA = 'controle_obras' 
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- Verificar dados órfãos
SELECT COUNT(*) as registros_horas FROM registro_horas;
SELECT COUNT(*) as orcamentos FROM orcamentos;
SELECT COUNT(*) as materiais FROM materiais;
```

## 🚨 Resolução de Problemas

### **Se algo der errado:**

1. **Restaurar backup:**
   ```sql
   mysql -u root -p controle_obras < backup_antes_correcoes.sql
   ```

2. **Verificar logs de erro:**
   ```sql
   SELECT * FROM correcoes_log WHERE status LIKE '%ERRO%';
   ```

3. **Executar scripts individuais:**
   - Execute apenas `fix_database_structure.sql`
   - Depois `fix_foreign_keys.sql`
   - Por último `fix_registro_horas_table.sql`

### **Problemas Comuns:**

#### **Erro de Foreign Key:**
```sql
SET FOREIGN_KEY_CHECKS = 0;
-- Execute as correções
SET FOREIGN_KEY_CHECKS = 1;
```

#### **Dados Órfãos:**
```sql
-- Verificar registros órfãos antes da correção
SELECT * FROM registro_horas 
WHERE id_obra NOT IN (SELECT obras_id FROM obras);
```

## ✅ Resultado Esperado

Após a execução bem-sucedida:

- ✅ **Estrutura do banco padronizada**
- ✅ **Foreign keys funcionando corretamente**
- ✅ **Dados íntegros e consistentes**
- ✅ **Performance otimizada**
- ✅ **Aplicação funcionando normalmente**
- ✅ **Zero alterações no código PHP**

## 📞 Suporte

Se encontrar problemas:

1. **Consulte a tabela `correcoes_log`**
2. **Verifique se o backup foi criado**
3. **Teste cada funcionalidade da aplicação**
4. **Documente qualquer erro encontrado**

---

**⚠️ IMPORTANTE:** Estas correções são **SEGURAS** e **NÃO ALTERAM** o código da aplicação. Apenas padronizam a estrutura do banco de dados para resolver inconsistências críticas.
