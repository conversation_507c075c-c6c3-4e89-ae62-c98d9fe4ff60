-- =====================================================
-- SCRIPT PARA CORRIGIR PROBLEMAS CRÍTICOS DO BANCO DE DADOS
-- =====================================================
-- Este script padroniza a estrutura do banco sem alterar funcionalidades
-- Baseado na análise da aplicação existente

-- =====================================================
-- 1. PADRONIZAR TABELA OBRAS
-- =====================================================

-- Verificar se a tabela obras existe e criar com estrutura padrão se não existir
CREATE TABLE IF NOT EXISTS `obras` (
  `obras_id` INT NOT NULL AUTO_INCREMENT,
  `nome_obra` VARCHAR(255) NOT NULL,
  `endereço` VARCHAR(255) NOT NULL,
  `data_inicio` DATE NOT NULL,
  `data_fim` DATE NULL,
  `status` VARCHAR(50) DEFAULT 'Em andamento',
  `descricao` TEXT NULL,
  `orcamento` DECIMAL(15, 2) NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`obras_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Adicionar colunas que podem estar faltando (sem erro se já existirem)
ALTER TABLE `obras` 
ADD COLUMN IF NOT EXISTS `status` VARCHAR(50) DEFAULT 'Em andamento',
ADD COLUMN IF NOT EXISTS `descricao` TEXT NULL,
ADD COLUMN IF NOT EXISTS `orcamento` DECIMAL(15, 2) NULL,
ADD COLUMN IF NOT EXISTS `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Adicionar alias para compatibilidade com scripts que usam 'localizacao'
-- (Criar view para compatibilidade sem alterar estrutura)
CREATE OR REPLACE VIEW `obras_compat` AS
SELECT 
    `obras_id`,
    `obras_id` AS `id_obra`,
    `nome_obra`,
    `endereço`,
    `endereço` AS `endereco`,
    `endereço` AS `localizacao`,
    `data_inicio`,
    `data_fim`,
    `data_fim` AS `data_fim_prevista`,
    `data_fim` AS `prazo`,
    `status`,
    `descricao`,
    `orcamento`,
    `data_criacao`
FROM `obras`;

-- =====================================================
-- 2. PADRONIZAR TABELA UTILIZADORES
-- =====================================================

-- Verificar se a tabela utilizadores existe
CREATE TABLE IF NOT EXISTS `utilizadores` (
  `id_utilizadores` INT NOT NULL AUTO_INCREMENT,
  `nome_utilizador` VARCHAR(255) NOT NULL,
  `password` VARCHAR(255) NOT NULL,
  `cargo_utilizador` VARCHAR(50) NOT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_utilizadores`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 3. PADRONIZAR TABELA REGISTRO_HORAS
-- =====================================================

-- Criar tabela de registro de horas com estrutura padrão
CREATE TABLE IF NOT EXISTS `registro_horas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `id_obra` INT NOT NULL,
  `id_usuario` INT NOT NULL,
  `horas` DECIMAL(5, 2) NOT NULL,
  `data_registro` DATE NOT NULL,
  `descricao` TEXT NULL,
  `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 4. PADRONIZAR TABELA ORCAMENTOS
-- =====================================================

-- Criar tabela de orçamentos com estrutura padrão
CREATE TABLE IF NOT EXISTS `orcamentos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `obra_id` INT NOT NULL,
  `descricao` TEXT NOT NULL,
  `valor` DECIMAL(15, 2) NOT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `data_aprovacao` DATE NULL,
  `status` VARCHAR(50) DEFAULT 'Em análise',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 5. PADRONIZAR TABELA MATERIAIS
-- =====================================================

-- Criar tabela de materiais com estrutura padrão
CREATE TABLE IF NOT EXISTS `materiais` (
  `material_id` INT NOT NULL AUTO_INCREMENT,
  `nome` VARCHAR(255) NOT NULL,
  `quantidade` DECIMAL(10, 2) NOT NULL,
  `unidade_medida` VARCHAR(50) DEFAULT 'unidades',
  `obras_id` INT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 6. ADICIONAR FOREIGN KEYS SEGURAS
-- =====================================================

-- Remover constraints existentes que podem estar conflitantes
SET FOREIGN_KEY_CHECKS = 0;

-- Adicionar foreign keys apenas se não existirem
-- Para registro_horas -> obras
SET @constraint_name = (SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                       WHERE TABLE_NAME = 'registro_horas' AND COLUMN_NAME = 'id_obra' 
                       AND CONSTRAINT_SCHEMA = DATABASE() LIMIT 1);

SET @sql = IF(@constraint_name IS NULL, 
    'ALTER TABLE `registro_horas` ADD CONSTRAINT `fk_registro_horas_obras` 
     FOREIGN KEY (`id_obra`) REFERENCES `obras` (`obras_id`) ON DELETE CASCADE ON UPDATE CASCADE',
    'SELECT "Foreign key registro_horas->obras já existe" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Para registro_horas -> utilizadores
SET @constraint_name = (SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                       WHERE TABLE_NAME = 'registro_horas' AND COLUMN_NAME = 'id_usuario' 
                       AND CONSTRAINT_SCHEMA = DATABASE() LIMIT 1);

SET @sql = IF(@constraint_name IS NULL, 
    'ALTER TABLE `registro_horas` ADD CONSTRAINT `fk_registro_horas_usuarios` 
     FOREIGN KEY (`id_usuario`) REFERENCES `utilizadores` (`id_utilizadores`) ON DELETE CASCADE ON UPDATE CASCADE',
    'SELECT "Foreign key registro_horas->utilizadores já existe" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Para orcamentos -> obras
SET @constraint_name = (SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                       WHERE TABLE_NAME = 'orcamentos' AND COLUMN_NAME = 'obra_id' 
                       AND CONSTRAINT_SCHEMA = DATABASE() LIMIT 1);

SET @sql = IF(@constraint_name IS NULL, 
    'ALTER TABLE `orcamentos` ADD CONSTRAINT `fk_orcamentos_obras` 
     FOREIGN KEY (`obra_id`) REFERENCES `obras` (`obras_id`) ON DELETE CASCADE ON UPDATE CASCADE',
    'SELECT "Foreign key orcamentos->obras já existe" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Para materiais -> obras
SET @constraint_name = (SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                       WHERE TABLE_NAME = 'materiais' AND COLUMN_NAME = 'obras_id' 
                       AND CONSTRAINT_SCHEMA = DATABASE() LIMIT 1);

SET @sql = IF(@constraint_name IS NULL, 
    'ALTER TABLE `materiais` ADD CONSTRAINT `fk_materiais_obras` 
     FOREIGN KEY (`obras_id`) REFERENCES `obras` (`obras_id`) ON DELETE SET NULL ON UPDATE CASCADE',
    'SELECT "Foreign key materiais->obras já existe" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 7. ADICIONAR ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para tabela obras
CREATE INDEX IF NOT EXISTS `idx_obras_status` ON `obras` (`status`);
CREATE INDEX IF NOT EXISTS `idx_obras_data_inicio` ON `obras` (`data_inicio`);

-- Índices para tabela registro_horas
CREATE INDEX IF NOT EXISTS `idx_registro_data` ON `registro_horas` (`data_registro`);
CREATE INDEX IF NOT EXISTS `idx_registro_obra` ON `registro_horas` (`id_obra`);
CREATE INDEX IF NOT EXISTS `idx_registro_usuario` ON `registro_horas` (`id_usuario`);

-- Índices para tabela orcamentos
CREATE INDEX IF NOT EXISTS `idx_orcamentos_obra` ON `orcamentos` (`obra_id`);
CREATE INDEX IF NOT EXISTS `idx_orcamentos_status` ON `orcamentos` (`status`);

-- Índices para tabela materiais
CREATE INDEX IF NOT EXISTS `idx_materiais_obra` ON `materiais` (`obras_id`);

-- =====================================================
-- 8. VALIDAR DADOS EXISTENTES
-- =====================================================

-- Corrigir status nulos
UPDATE `obras` SET `status` = 'Em andamento' WHERE `status` IS NULL OR `status` = '';

-- Corrigir datas nulas em registro_horas
UPDATE `registro_horas` SET `data_registro` = CURDATE() WHERE `data_registro` IS NULL;

-- =====================================================
-- 9. CRIAR VIEWS DE COMPATIBILIDADE
-- =====================================================

-- View para compatibilidade com scripts que usam diferentes nomes de campos
CREATE OR REPLACE VIEW `obras_view` AS
SELECT 
    `obras_id` AS `id`,
    `obras_id`,
    `obras_id` AS `id_obra`,
    `nome_obra`,
    `endereço`,
    `endereço` AS `endereco`,
    `endereço` AS `localizacao`,
    `data_inicio`,
    `data_fim`,
    `data_fim` AS `data_fim_prevista`,
    `data_fim` AS `data_fim_real`,
    `data_fim` AS `prazo`,
    `status`,
    `descricao`,
    `orcamento`,
    `data_criacao`
FROM `obras`;

-- =====================================================
-- SCRIPT CONCLUÍDO
-- =====================================================
-- Este script padroniza a estrutura do banco de dados
-- mantendo compatibilidade com a aplicação existente
-- =====================================================
