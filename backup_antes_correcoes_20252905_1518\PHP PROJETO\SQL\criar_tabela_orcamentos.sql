-- <PERSON><PERSON><PERSON> tabel<PERSON> de orça<PERSON>
CREATE TABLE IF NOT EXISTS `orcamentos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `obra_id` INT NOT NULL,
  `descricao` TEXT NOT NULL,
  `valor` DECIMAL(15, 2) NOT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `data_aprovacao` DATE NULL,
  `status` VARCHAR(50) DEFAULT 'Em análise',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_orcamentos_obras`
    FOREIGN KEY (`obra_id`)
    REFERENCES `obras` (`obras_id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
);