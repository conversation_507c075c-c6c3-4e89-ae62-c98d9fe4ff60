<?php
session_start();

// Incluir arquivos necessários
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';
require_once 'conexao.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário tem permissão (apenas nível 1 - administrador)
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')) {
    $_SESSION['AccessError'] = "Acesso negado. Apenas administradores podem eliminar utilizadores.";
    header("Location: Projeto.php");
    exit();
}

// Verificar se o ID do utilizador foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['mensagem'] = "ID do utilizador não fornecido.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: listar_utilizadores.php");
    exit();
}

// Verificar token CSRF
if (!isset($_GET['csrf_token']) || !verifyCSRFToken($_GET['csrf_token'])) {
    $_SESSION['mensagem'] = "Erro de segurança. Por favor, tente novamente.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: listar_utilizadores.php");
    exit();
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Obter o ID do utilizador a ser eliminado
$id_utilizador = mysqli_real_escape_string($conn, $_GET['id']);

// Verificar se o utilizador está tentando eliminar a si mesmo
if ($id_utilizador == $_SESSION['user_id']) {
    $_SESSION['mensagem'] = "Não é possível eliminar o utilizador atual.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: listar_utilizadores.php");
    exit();
}

// Verificar se o utilizador existe
$query_check = "SELECT id_utilizadores, nome_utilizador, cargo_utilizador FROM utilizadores WHERE id_utilizadores = ?";
$stmt_check = mysqli_prepare($conn, $query_check);
mysqli_stmt_bind_param($stmt_check, "i", $id_utilizador);
mysqli_stmt_execute($stmt_check);
mysqli_stmt_store_result($stmt_check);

if (mysqli_stmt_num_rows($stmt_check) == 0) {
    mysqli_stmt_close($stmt_check);
    mysqli_close($conn);
    
    $_SESSION['mensagem'] = "Utilizador não encontrado.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: listar_utilizadores.php");
    exit();
}

// Obter informações do utilizador
mysqli_stmt_bind_result($stmt_check, $id, $nome, $cargo);
mysqli_stmt_fetch($stmt_check);
mysqli_stmt_close($stmt_check);

// Verificar se está tentando eliminar outro administrador
if ($cargo == '1' && $_SESSION['cargo_utilizador'] == '1') {
    // Contar quantos administradores existem
    $query_count_admins = "SELECT COUNT(*) FROM utilizadores WHERE cargo_utilizador = '1'";
    $result_count = mysqli_query($conn, $query_count_admins);
    $row = mysqli_fetch_array($result_count);
    $num_admins = $row[0];
    
    // Se houver apenas um administrador, não permitir a exclusão
    if ($num_admins <= 1) {
        mysqli_close($conn);
        
        $_SESSION['mensagem'] = "Não é possível eliminar o único administrador do sistema.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: listar_utilizadores.php");
        exit();
    }
}

// Iniciar transação
mysqli_begin_transaction($conn);

try {
    // Verificar se há registros de horas associados ao utilizador
    $query_check_horas = "SELECT COUNT(*) FROM registro_horas WHERE id_usuario = ?";
    $stmt_check_horas = mysqli_prepare($conn, $query_check_horas);
    
    if ($stmt_check_horas) {
        mysqli_stmt_bind_param($stmt_check_horas, "i", $id_utilizador);
        mysqli_stmt_execute($stmt_check_horas);
        mysqli_stmt_bind_result($stmt_check_horas, $count_horas);
        mysqli_stmt_fetch($stmt_check_horas);
        mysqli_stmt_close($stmt_check_horas);
        
        // Se houver registros de horas, excluí-los primeiro
        if ($count_horas > 0) {
            $query_delete_horas = "DELETE FROM registro_horas WHERE id_usuario = ?";
            $stmt_delete_horas = mysqli_prepare($conn, $query_delete_horas);
            mysqli_stmt_bind_param($stmt_delete_horas, "i", $id_utilizador);
            
            if (!mysqli_stmt_execute($stmt_delete_horas)) {
                throw new Exception("Erro ao eliminar registros de horas: " . mysqli_stmt_error($stmt_delete_horas));
            }
            
            mysqli_stmt_close($stmt_delete_horas);
        }
    }
    
    // Eliminar o utilizador
    $query_delete = "DELETE FROM utilizadores WHERE id_utilizadores = ?";
    $stmt_delete = mysqli_prepare($conn, $query_delete);
    mysqli_stmt_bind_param($stmt_delete, "i", $id_utilizador);
    
    if (!mysqli_stmt_execute($stmt_delete)) {
        throw new Exception("Erro ao eliminar utilizador: " . mysqli_stmt_error($stmt_delete));
    }
    
    mysqli_stmt_close($stmt_delete);
    
    // Confirmar transação
    mysqli_commit($conn);
    
    $_SESSION['mensagem'] = "Utilizador '{$nome}' eliminado com sucesso.";
    $_SESSION['tipo_mensagem'] = "success";
    
} catch (Exception $e) {
    // Reverter transação em caso de erro
    mysqli_rollback($conn);
    
    $_SESSION['mensagem'] = "Erro ao eliminar utilizador: " . $e->getMessage();
    $_SESSION['tipo_mensagem'] = "danger";
}

// Fechar conexão
mysqli_close($conn);

// Redirecionar para a página de listagem
header("Location: listar_utilizadores.php");
exit();
?>
