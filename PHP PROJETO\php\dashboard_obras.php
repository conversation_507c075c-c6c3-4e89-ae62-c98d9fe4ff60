<?php
// Função para obter dados do dashboard
function getDashboardData($conn) {
    // Array para armazenar os dados do dashboard
    $data = [
        "obras_ativas" => 0,
        "obras_concluidas" => 0,
        "funcionarios" => 0,
        "orcamento_total" => 0.00,
        "obras_progresso" => [],
        "proximos_prazos" => [],
        "ultimos_registros" => [],
        "registros_horas" => []
    ];
    
    // Verificar se a conexão é válida
    if (!$conn) {
        error_log("Erro: Conexão inválida em getDashboardData");
        return $data;
    }
    
    try {
        // Determinar nomes das colunas e chaves primárias
        // Chave primária da tabela obras
        $result = mysqli_query($conn, "SHOW KEYS FROM obras WHERE Key_name = 'PRIMARY'");
        $primary_key_obras = 'id'; // Valor padrão
        if ($result && $row = mysqli_fetch_assoc($result)) {
            $primary_key_obras = $row['Column_name'];
        }
        
        // Chave primária da tabela utilizadores
        $result = mysqli_query($conn, "SHOW KEYS FROM utilizadores WHERE Key_name = 'PRIMARY'");
        $primary_key_utilizadores = 'id'; // Valor padrão
        if ($result && $row = mysqli_fetch_assoc($result)) {
            $primary_key_utilizadores = $row['Column_name'];
        }
        
        // Verificar se a coluna nome_obra existe
        $result = mysqli_query($conn, "SHOW COLUMNS FROM obras LIKE 'nome_obra'");
        $coluna_nome = 'nome_obra';
        if (mysqli_num_rows($result) == 0) {
            // Tentar encontrar uma coluna alternativa para o nome
            $result = mysqli_query($conn, "SHOW COLUMNS FROM obras LIKE 'nome'");
            if (mysqli_num_rows($result) > 0) {
                $coluna_nome = 'nome';
            }
        }
        
        // Verificar a coluna do nome do utilizador
        $result = mysqli_query($conn, "DESCRIBE utilizadores");
        $coluna_nome_utilizador = 'nome_utilizador'; // Valor padrão
        if ($result) {
            $colunas_utilizadores = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $colunas_utilizadores[] = $row['Field'];
            }
            
            // Tentar encontrar a coluna que contém o nome do utilizador
            $possiveis_colunas_nome = ['nome', 'nome_utilizador', 'nome_completo', 'username'];
            foreach ($possiveis_colunas_nome as $coluna) {
                if (in_array($coluna, $colunas_utilizadores)) {
                    $coluna_nome_utilizador = $coluna;
                    break;
                }
            }
        }
        
        // 1. Contar obras ativas
        $query = "SELECT COUNT(*) as total FROM obras WHERE status = 'Em andamento'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $data["obras_ativas"] = (int)$row["total"];
        }
        
        // 2. Contar obras concluídas
        $query = "SELECT COUNT(*) as total FROM obras WHERE status = 'Concluída'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $data["obras_concluidas"] = (int)$row["total"];
        }
        
        // 3. Contar funcionários
        $query = "SELECT COUNT(*) as total FROM utilizadores";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $data["funcionarios"] = (int)$row["total"];
        }
        
        // 4. Calcular orçamento total (soma dos orçamentos das obras + orçamentos aprovados)
        $query = "SELECT SUM(orcamento) as total FROM obras";
        $result = mysqli_query($conn, $query);
        $orcamento_obras = 0;
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $orcamento_obras = (float)($row["total"] ?? 0);
        }

        // Verificar se a tabela orcamentos existe
        $result = mysqli_query($conn, "SHOW TABLES LIKE 'orcamentos'");
        $tabela_orcamentos_existe = mysqli_num_rows($result) > 0;

        // Adicionar orçamentos aprovados se a tabela existir
        $orcamento_aprovados = 0;
        if ($tabela_orcamentos_existe) {
            $query = "SELECT SUM(valor) as total FROM orcamentos WHERE status = 'Aprovado'";
            $result = mysqli_query($conn, $query);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                $orcamento_aprovados = (float)($row["total"] ?? 0);
            }
        }

        // Somar os dois valores para o orçamento total
        $data["orcamento_total"] = $orcamento_obras + $orcamento_aprovados;
        error_log("Orçamento obras: " . $orcamento_obras . ", Orçamentos aprovados: " . $orcamento_aprovados . ", Total: " . $data["orcamento_total"]);

        // Diagnóstico para orçamento total
        error_log("=== DIAGNÓSTICO DE ORÇAMENTO TOTAL ===");
        error_log("Verificando tabela obras...");
        $query_diagnostico = "SELECT COUNT(*) as total_registros, SUM(orcamento) as soma_orcamentos FROM obras";
        $result_diagnostico = mysqli_query($conn, $query_diagnostico);
        if ($result_diagnostico) {
            $row_diagnostico = mysqli_fetch_assoc($result_diagnostico);
            error_log("Total de registros na tabela obras: " . $row_diagnostico['total_registros']);
            error_log("Soma dos orçamentos na tabela obras: " . $row_diagnostico['soma_orcamentos']);
        } else {
            error_log("Erro ao consultar tabela obras: " . mysqli_error($conn));
        }

        // Verificar tabela orcamentos
        error_log("Verificando tabela orcamentos...");
        $result_diagnostico = mysqli_query($conn, "SHOW TABLES LIKE 'orcamentos'");
        if (mysqli_num_rows($result_diagnostico) > 0) {
            error_log("Tabela orcamentos existe");
            $query_diagnostico = "SELECT COUNT(*) as total_registros, SUM(valor) as soma_valores, COUNT(CASE WHEN status = 'Aprovado' THEN 1 END) as total_aprovados, SUM(CASE WHEN status = 'Aprovado' THEN valor ELSE 0 END) as soma_aprovados FROM orcamentos";
            $result_diagnostico = mysqli_query($conn, $query_diagnostico);
            if ($result_diagnostico) {
                $row_diagnostico = mysqli_fetch_assoc($result_diagnostico);
                error_log("Total de registros na tabela orcamentos: " . $row_diagnostico['total_registros']);
                error_log("Soma de todos os valores na tabela orcamentos: " . $row_diagnostico['soma_valores']);
                error_log("Total de orçamentos aprovados: " . $row_diagnostico['total_aprovados']);
                error_log("Soma dos valores dos orçamentos aprovados: " . $row_diagnostico['soma_aprovados']);
            } else {
                error_log("Erro ao consultar tabela orcamentos: " . mysqli_error($conn));
            }
        } else {
            error_log("Tabela orcamentos não existe");
        }
        
        // 5. Obter obras com progresso - aumentado o limite para mostrar todas as obras
        $query = "SELECT $coluna_nome as nome_obra, status, progresso, orcamento FROM obras ORDER BY FIELD(status, 'Em andamento', 'Concluída', 'Pausada', 'Cancelada') LIMIT 50";
        $result = mysqli_query($conn, $query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                // Definir progresso como 100% para obras concluídas
                if (isset($row['status']) && $row['status'] == 'Concluída') {
                    $row['progresso'] = 100;
                } elseif (!isset($row['progresso']) || $row['progresso'] === null) {
                    // Definir um valor padrão para progresso se não estiver definido
                    $row['progresso'] = 0;
                }
                $data["obras_progresso"][] = $row;
            }
        } else {
            // Se a consulta falhar, tentar uma consulta mais simples
            $query_simples = "SELECT * FROM obras LIMIT 50";
            $result = mysqli_query($conn, $query_simples);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    // Preparar os dados no formato esperado
                    $status = $row['status'] ?? 'Em andamento';
                    $progresso = ($status == 'Concluída') ? 100 : ($row['progresso'] ?? 0);
                    
                    $data["obras_progresso"][] = [
                        'nome_obra' => $row[$coluna_nome] ?? 'Sem nome',
                        'status' => $status,
                        'progresso' => $progresso,
                        'orcamento' => (float)($row['orcamento'] ?? 0)
                    ];
                }
            }
        }

        // Verificar se há obras no array
        if (empty($data["obras_progresso"])) {
            error_log("Nenhuma obra encontrada para o status. Verificando a tabela obras...");
            
            // Verificar se há registros na tabela obras
            $query = "SELECT COUNT(*) as total FROM obras";
            $result = mysqli_query($conn, $query);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                error_log("Total de obras na tabela: " . $row['total']);
            }
            
            // Verificar a estrutura da tabela obras
            $result = mysqli_query($conn, "DESCRIBE obras");
            if ($result) {
                $colunas = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $colunas[] = $row['Field'] . ' (' . $row['Type'] . ')';
                }
                error_log("Estrutura da tabela obras: " . implode(", ", $colunas));
            }
        }
        
        // 6. Obter próximos prazos - modificado para incluir apenas obras não concluídas e ordenar por data
        $query = "SELECT $coluna_nome as nome_obra, data_fim as prazo, status 
                  FROM obras 
                  WHERE data_fim IS NOT NULL AND status != 'Concluída' 
                  ORDER BY data_fim ASC 
                  LIMIT 10";
        $result = mysqli_query($conn, $query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $data["proximos_prazos"][] = $row;
            }
            error_log("Próximos prazos: " . count($data["proximos_prazos"]) . " registros encontrados");
        } else {
            error_log("Erro ao consultar próximos prazos: " . mysqli_error($conn));
        }
        
        // 7. Verificar a estrutura da tabela registro_horas
        $result = mysqli_query($conn, "DESCRIBE registro_horas");
        $colunas_registro = [];
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $colunas_registro[] = $row['Field'];
            }
        }
        
        // Determinar o nome da coluna para o ID do utilizador
        $coluna_id_usuario = null;
        $possiveis_colunas = ['id_usuario', 'id_utilizador', 'utilizador_id', 'user_id'];
        foreach ($possiveis_colunas as $coluna) {
            if (in_array($coluna, $colunas_registro)) {
                $coluna_id_usuario = $coluna;
                break;
            }
        }
        
        // 8. Obter últimos registros de horas
        if ($coluna_id_usuario) {
            $query = "SELECT r.data_registro, r.horas, r.descricao, o.$coluna_nome as nome_obra, u.$coluna_nome_utilizador as nome_utilizador 
                     FROM registro_horas r 
                     LEFT JOIN obras o ON r.id_obra = o.$primary_key_obras 
                     LEFT JOIN utilizadores u ON r.$coluna_id_usuario = u.$primary_key_utilizadores 
                     ORDER BY r.data_registro DESC LIMIT 5";
        } else {
            // Consulta simplificada sem o JOIN com utilizadores
            $query = "SELECT r.data_registro, r.horas, r.descricao, o.$coluna_nome as nome_obra 
                     FROM registro_horas r 
                     LEFT JOIN obras o ON r.id_obra = o.$primary_key_obras 
                     ORDER BY r.data_registro DESC LIMIT 5";
        }
        
        $result = mysqli_query($conn, $query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $data["ultimos_registros"][] = $row;
            }
        } else {
            // Tentar uma consulta mais simples como fallback
            $query_simples = "SELECT data_registro, horas, descricao FROM registro_horas ORDER BY data_registro DESC LIMIT 5";
            $result = mysqli_query($conn, $query_simples);
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $data["ultimos_registros"][] = $row;
                }
            }
        }
        
        // 6. Obter registros de horas recentes - Consulta corrigida e mais robusta
        $query = "SELECT rh.*, o.nome_obra, u.nome_utilizador 
                  FROM registro_horas rh 
                  LEFT JOIN obras o ON rh.id_obra = o.obras_id 
                  LEFT JOIN utilizadores u ON rh.id_usuario = u.id_utilizadores 
                  ORDER BY rh.data_registro DESC 
                  LIMIT 10";
        $result = mysqli_query($conn, $query);

        if ($result && mysqli_num_rows($result) > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                $data["registros_horas"][] = $row;
            }
        } else {
            // Verificar estrutura da tabela para diagnóstico
            $table_info_query = "DESCRIBE registro_horas";
            $table_info = mysqli_query($conn, $table_info_query);
            $colunas = [];
            
            if ($table_info) {
                while ($col = mysqli_fetch_assoc($table_info)) {
                    $colunas[] = $col['Field'];
                }
            }
            
            // Determinar nomes corretos das colunas
            $id_obra_col = in_array('id_obra', $colunas) ? 'id_obra' : 'obra_id';
            $id_usuario_col = in_array('id_usuario', $colunas) ? 'id_usuario' : 
                             (in_array('id_utilizador', $colunas) ? 'id_utilizador' : 'usuario_id');
            $data_col = in_array('data_registro', $colunas) ? 'data_registro' : 
                       (in_array('data_registo', $colunas) ? 'data_registo' : 'data');
            
            // Verificar tabela obras
            $obras_info_query = "DESCRIBE obras";
            $obras_info = mysqli_query($conn, $obras_info_query);
            $colunas_obras = [];
            
            if ($obras_info) {
                while ($col = mysqli_fetch_assoc($obras_info)) {
                    $colunas_obras[] = $col['Field'];
                }
            }
            
            $id_obra_tab = in_array('obras_id', $colunas_obras) ? 'obras_id' : 
                          (in_array('id_obra', $colunas_obras) ? 'id_obra' : 'id');
            $nome_obra_col = in_array('nome_obra', $colunas_obras) ? 'nome_obra' : 'nome';
            
            // Verificar tabela utilizadores
            $users_info_query = "DESCRIBE utilizadores";
            $users_info = mysqli_query($conn, $users_info_query);
            $colunas_users = [];
            
            if ($users_info) {
                while ($col = mysqli_fetch_assoc($users_info)) {
                    $colunas_users[] = $col['Field'];
                }
            }
            
            $id_user_tab = in_array('id_utilizadores', $colunas_users) ? 'id_utilizadores' : 'id';
            $nome_user_col = in_array('nome_utilizador', $colunas_users) ? 'nome_utilizador' : 'nome';
            
            // Consulta alternativa com os nomes corretos das colunas
            $query_alt = "SELECT rh.*, o.$nome_obra_col as nome_obra, u.$nome_user_col as nome_utilizador 
                         FROM registro_horas rh 
                         LEFT JOIN obras o ON rh.$id_obra_col = o.$id_obra_tab 
                         LEFT JOIN utilizadores u ON rh.$id_usuario_col = u.$id_user_tab 
                         ORDER BY rh.$data_col DESC 
                         LIMIT 10";
            
            $result_alt = mysqli_query($conn, $query_alt);
            if ($result_alt && mysqli_num_rows($result_alt) > 0) {
                while ($row = mysqli_fetch_assoc($result_alt)) {
                    $data["registros_horas"][] = $row;
                }
            } else {
                // Consulta simplificada sem joins para diagnóstico
                $simple_query = "SELECT * FROM registro_horas ORDER BY id DESC LIMIT 10";
                $simple_result = mysqli_query($conn, $simple_query);
                
                if ($simple_result && mysqli_num_rows($simple_result) > 0) {
                    while ($row = mysqli_fetch_assoc($simple_result)) {
                        $data["registros_horas"][] = $row;
                    }
                } else {
                    // Verificar se a tabela existe e tem dados
                    $check_table = mysqli_query($conn, "SELECT COUNT(*) as count FROM registro_horas");
                    if ($check_table) {
                        $count = mysqli_fetch_assoc($check_table);
                        if ($count['count'] == 0) {
                            // Inserir dados de exemplo se a tabela estiver vazia
                            $insert_sample = "INSERT INTO registro_horas (id_obra, id_usuario, horas, data_registro, descricao) 
                                             SELECT 
                                                (SELECT $id_obra_tab FROM obras LIMIT 1),
                                                (SELECT $id_user_tab FROM utilizadores LIMIT 1),
                                                8.5,
                                                CURDATE(),
                                                'Registro de exemplo'
                                             FROM dual
                                             WHERE EXISTS (SELECT 1 FROM obras LIMIT 1) 
                                             AND EXISTS (SELECT 1 FROM utilizadores LIMIT 1)";
                            mysqli_query($conn, $insert_sample);
                            
                            // Tentar novamente a consulta
                            $retry_query = "SELECT * FROM registro_horas ORDER BY id DESC LIMIT 10";
                            $retry_result = mysqli_query($conn, $retry_query);
                            
                            if ($retry_result && mysqli_num_rows($retry_result) > 0) {
                                while ($row = mysqli_fetch_assoc($retry_result)) {
                                    $data["registros_horas"][] = $row;
                                }
                            }
                        }
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("Exceção em getDashboardData: " . $e->getMessage());
    }
    
    return $data;
}
?>





