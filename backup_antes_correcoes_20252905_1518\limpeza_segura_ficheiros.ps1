# Script PowerShell para limpeza segura de ficheiros desnecessários
# Manté<PERSON> todas as funcionalidades essenciais do sistema

Write-Host "=== SCRIPT DE LIMPEZA SEGURA DE FICHEIROS DESNECESSÁRIOS ===" -ForegroundColor Green
Write-Host "Este script irá eliminar apenas ficheiros seguros, mantendo todas as funcionalidades." -ForegroundColor Yellow
Write-Host ""

# Contador de ficheiros eliminados
$count = 0

# Função para eliminar ficheiro se existir
function Safe-Remove {
    param([string]$Path)
    
    if (Test-Path $Path -PathType Leaf) {
        Remove-Item $Path -Force
        Write-Host "✓ Eliminado: $Path" -ForegroundColor Green
        $script:count++
    } else {
        Write-Host "- Não encontrado: $Path" -ForegroundColor Gray
    }
}

# Função para eliminar diretório se existir
function Safe-Remove-Dir {
    param([string]$Path)
    
    if (Test-Path $Path -PathType Container) {
        Remove-Item $Path -Recurse -Force
        Write-Host "✓ Diretório eliminado: $Path" -ForegroundColor Green
        $script:count++
    } else {
        Write-Host "- Diretório não encontrado: $Path" -ForegroundColor Gray
    }
}

Write-Host "1. ELIMINANDO FICHEIROS DE TESTE E DEBUG..." -ForegroundColor Cyan
Write-Host "============================================="

# Ficheiros de teste na raiz
Safe-Remove "test_app_functions.php"
Safe-Remove "output.html"

# Ficheiros de teste e debug no diretório php
Safe-Remove "PHP PROJETO\php\test_admin.php"
Safe-Remove "PHP PROJETO\php\test_admin_login.php"
Safe-Remove "PHP PROJETO\php\test_admin_menu.php"
Safe-Remove "PHP PROJETO\php\test_database.php"
Safe-Remove "PHP PROJETO\php\test_menu.php"
Safe-Remove "PHP PROJETO\php\test_security.php"

# Ficheiros de debug
Safe-Remove "PHP PROJETO\php\debug_admin.php"
Safe-Remove "PHP PROJETO\php\debug_db.php"
Safe-Remove "PHP PROJETO\php\debug_form.js"
Safe-Remove "PHP PROJETO\php\debug_menu.js"
Safe-Remove "PHP PROJETO\php\debug_session.php"
Safe-Remove "PHP PROJETO\php\debug_csrf.js"

# Ficheiros de verificação e diagnóstico
Safe-Remove "PHP PROJETO\php\check_admin.php"
Safe-Remove "PHP PROJETO\php\check_file.php"
Safe-Remove "PHP PROJETO\php\check_session.php"
Safe-Remove "PHP PROJETO\php\diagnostico.php"

Write-Host ""
Write-Host "2. ELIMINANDO FICHEIROS DE ADMINISTRAÇÃO TEMPORÁRIOS..." -ForegroundColor Cyan
Write-Host "======================================================="

Safe-Remove "PHP PROJETO\php\force_admin.php"
Safe-Remove "PHP PROJETO\php\direct_admin_access.php"
Safe-Remove "PHP PROJETO\php\set_admin.php"
Safe-Remove "PHP PROJETO\php\go_to_admin.php"
Safe-Remove "PHP PROJETO\php\admin_acesso.php"
Safe-Remove "PHP PROJETO\php\admin_button.php"
Safe-Remove "PHP PROJETO\php\admin_page_copy.php"

Write-Host ""
Write-Host "3. ELIMINANDO FICHEIROS DE CONFIGURAÇÃO TEMPORÁRIOS..." -ForegroundColor Cyan
Write-Host "======================================================"

Safe-Remove "PHP PROJETO\php\corrigir_tabela_obras.php"
Safe-Remove "PHP PROJETO\php\copiar_imagens_manual.php"
Safe-Remove "PHP PROJETO\php\atualizar_proximos_prazos.php"
Safe-Remove "PHP PROJETO\php\clear_session.php"

Write-Host ""
Write-Host "4. ELIMINANDO FICHEIROS JAVASCRIPT DE DEBUG..." -ForegroundColor Cyan
Write-Host "=============================================="

# MANTER: fix_admin_menu.js, csrf_fix.js, csrf_init.js (são utilizados)
# ELIMINAR: apenas os de debug
Safe-Remove "PHP PROJETO\php\admin_menu_debug.js"
Safe-Remove "PHP PROJETO\php\debug_menu.js"
Safe-Remove "PHP PROJETO\php\menu_fix.js"

Write-Host ""
Write-Host "5. ELIMINANDO SCRIPTS DE REVERSÃO..." -ForegroundColor Cyan
Write-Host "===================================="

Safe-Remove "php\revert_paths.php"

Write-Host ""
Write-Host "6. ELIMINANDO PROJETO HOTEL COMPLETO..." -ForegroundColor Cyan
Write-Host "======================================="

Safe-Remove-Dir "Hotel_Estrela_Mar_(php)"

Write-Host ""
Write-Host "7. ELIMINANDO FICHEIROS HTML ESTÁTICOS..." -ForegroundColor Cyan
Write-Host "========================================="

Safe-Remove-Dir "PHP PROJETO\HTML"

Write-Host ""
Write-Host "8. ELIMINANDO FICHEIROS DE EXEMPLO..." -ForegroundColor Cyan
Write-Host "====================================="

Safe-Remove-Dir "PHP action"

Write-Host ""
Write-Host "9. ELIMINANDO DOCUMENTAÇÃO E IMAGENS AUXILIARES..." -ForegroundColor Cyan
Write-Host "=================================================="

Safe-Remove-Dir "Word"
Safe-Remove-Dir "PHP PROJETO\imagens_aux"
Safe-Remove "Gestao-Digital-de-Obras-Projeto-de-Aplicacao-Web.pptx.pdf.pptx"
Safe-Remove "Planeador de projetos Gantt1 (version 1).xlsb.xlsx"

Write-Host ""
Write-Host "10. ELIMINANDO LOGS E FICHEIROS TEMPORÁRIOS..." -ForegroundColor Cyan
Write-Host "=============================================="

Safe-Remove-Dir "PHP PROJETO\php\logs"

Write-Host ""
Write-Host "=== RESUMO DA LIMPEZA ===" -ForegroundColor Green
Write-Host "Ficheiros/diretórios eliminados: $count" -ForegroundColor Yellow
Write-Host ""
Write-Host "✅ FICHEIROS MANTIDOS (ESSENCIAIS):" -ForegroundColor Green
Write-Host "- Todas as páginas principais (Projeto*.php)"
Write-Host "- Ficheiros de processamento (processar_*.php)"
Write-Host "- Ficheiros de segurança (conexao.php, verificar_permissao.php, etc.)"
Write-Host "- Ficheiros CSS essenciais"
Write-Host "- Ficheiros JavaScript funcionais (fix_admin_menu.js, csrf_*.js)"
Write-Host "- Imagens utilizadas (Imagem1.png)"
Write-Host ""
Write-Host "🗑️ FICHEIROS ELIMINADOS:" -ForegroundColor Red
Write-Host "- Ficheiros de teste e debug"
Write-Host "- Projeto Hotel completo"
Write-Host "- Ficheiros HTML estáticos"
Write-Host "- Documentação e imagens auxiliares"
Write-Host "- Scripts temporários e de reversão"
Write-Host ""
Write-Host "✅ Limpeza concluída com sucesso!" -ForegroundColor Green
Write-Host "✅ Todas as funcionalidades principais foram preservadas." -ForegroundColor Green

# Pausa para o utilizador ver o resultado
Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
