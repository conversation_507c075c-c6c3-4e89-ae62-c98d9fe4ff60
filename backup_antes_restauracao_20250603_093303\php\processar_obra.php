<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['mensagem'] = "Erro de segurança. Por favor, tente novamente.";
        $_SESSION['tipo_mensagem'] = "danger";
        header("Location: Projeto pag 2.php");
        exit();
    }

    $conn = connectToDatabase();
    $erro = false;

    // Preparar e limpar os dados
    $nome_obra = mysqli_real_escape_string($conn, $_POST['nome_obra']);
    // Verificar se o campo é 'localizacao' ou 'endereco'
    $endereco = isset($_POST['localizacao']) ? mysqli_real_escape_string($conn, $_POST['localizacao']) :
                (isset($_POST['endereco']) ? mysqli_real_escape_string($conn, $_POST['endereco']) : '');
    $data_inicio_raw = mysqli_real_escape_string($conn, $_POST['data_inicio']);
    // Verificar se o status foi fornecido, senão usar padrão
    $status = isset($_POST['status']) && !empty($_POST['status']) ?
              mysqli_real_escape_string($conn, $_POST['status']) : "Em andamento";

    // Validar campos obrigatórios
    if (empty($nome_obra)) {
        $erro = true;
        $_SESSION['mensagem'] = "O nome da obra é obrigatório.";
        $_SESSION['tipo_mensagem'] = "danger";
    }

    if (empty($endereco)) {
        $erro = true;
        $_SESSION['mensagem'] = "A localização da obra é obrigatória.";
        $_SESSION['tipo_mensagem'] = "danger";
    }

    if (empty($data_inicio_raw)) {
        $erro = true;
        $_SESSION['mensagem'] = "A data de início é obrigatória.";
        $_SESSION['tipo_mensagem'] = "danger";
    }

    // Validar data de início usando a função de validação
    $data_inicio = null;
    if (!empty($data_inicio_raw)) {
        $data_inicio = validateDate($data_inicio_raw);
        if ($data_inicio === false) {
            $erro = true;
            $_SESSION['mensagem'] = "Data de início inválida. Use uma data válida no formato AAAA-MM-DD.";
            $_SESSION['tipo_mensagem'] = "danger";
        }
    }

    // Validar data de fim (se fornecida) usando a função de validação
    $data_fim = null;
    if (isset($_POST['data_fim']) && !empty($_POST['data_fim'])) {
        $data_fim_raw = mysqli_real_escape_string($conn, $_POST['data_fim']);
        $data_fim = validateDate($data_fim_raw);

        if ($data_fim === false) {
            $erro = true;
            $_SESSION['mensagem'] = "Data de fim inválida. Use uma data válida no formato AAAA-MM-DD.";
            $_SESSION['tipo_mensagem'] = "danger";
        }
    }

    // Continuar apenas se não houver erros
    if (!$erro) {
        // Verificar a estrutura da tabela obras
        $query_describe = "DESCRIBE obras";
        $result_describe = mysqli_query($conn, $query_describe);
        $colunas = [];

        while ($row = mysqli_fetch_assoc($result_describe)) {
            $colunas[] = $row['Field'];
        }

        // Determinar o nome correto das colunas
        $coluna_nome = in_array('nome_obra', $colunas) ? 'nome_obra' :
                      (in_array('nome', $colunas) ? 'nome' : 'nome_obra');

        $coluna_endereco = in_array('endereço', $colunas) ? 'endereço' :
                          (in_array('endereco', $colunas) ? 'endereco' :
                          (in_array('localizacao', $colunas) ? 'localizacao' : 'endereço'));

        $coluna_data_inicio = in_array('data_inicio', $colunas) ? 'data_inicio' :
                             (in_array('data_inicio_real', $colunas) ? 'data_inicio_real' : 'data_inicio');

        $coluna_data_fim = in_array('data_fim', $colunas) ? 'data_fim' :
                          (in_array('data_fim_prevista', $colunas) ? 'data_fim_prevista' :
                          (in_array('prazo', $colunas) ? 'prazo' : 'data_fim'));

        // Construir a query dinamicamente com base nas colunas existentes
        if ($data_fim !== null) {
            $query = "INSERT INTO obras ($coluna_nome, $coluna_endereco, $coluna_data_inicio, $coluna_data_fim, status) VALUES (?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "sssss", $nome_obra, $endereco, $data_inicio, $data_fim, $status);
        } else {
            $query = "INSERT INTO obras ($coluna_nome, $coluna_endereco, $coluna_data_inicio, status) VALUES (?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, "ssss", $nome_obra, $endereco, $data_inicio, $status);
        }

        if (mysqli_stmt_execute($stmt)) {
            $_SESSION['mensagem'] = "Obra registrada com sucesso!";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao registrar obra: " . mysqli_stmt_error($stmt);
            $_SESSION['tipo_mensagem'] = "danger";
        }

        mysqli_stmt_close($stmt);
    }

    mysqli_close($conn);

    // Redirecionar de volta para a página de obras
    header("Location: Projeto pag 2.php");
    exit();
}
?>





