<?php
/**
 * Arquivo de conexão com o banco de dados
 * Contém funções para estabelecer e gerenciar conexões com o banco de dados
 */

// Incluir arquivo de configuração
require_once 'config.php';

/**
 * Função para conectar ao banco de dados
 * @return mysqli|false Retorna a conexão ou false em caso de erro
 */
function connectToDatabase() {
    // Criar conexão
    $conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

    // Verificar conexão
    if ($conn->connect_error) {
        // Log do erro
        error_log("Falha na conexão com o banco de dados: " . $conn->connect_error);

        // Em produção, não mostrar detalhes do erro
        die("Erro ao conectar ao banco de dados. Por favor, tente novamente mais tarde.");
    }

    // Definir charset
    $conn->set_charset(DB_CHARSET);

    return $conn;
}

/**
 * Função para executar consultas com tratamento de erros
 * @param mysqli $conn Conexão com o banco de dados
 * @param string $sql Consulta SQL
 * @return mysqli_result|bool Resultado da consulta ou false em caso de erro
 */
function executeQuery($conn, $sql) {
    $result = $conn->query($sql);
    if ($result === false) {
        error_log("Erro na consulta SQL: " . $conn->error . " - Query: " . $sql);
        return null;
    }
    return $result;
}

/**
 * Função para executar consultas preparadas com tratamento de erros
 * @param mysqli $conn Conexão com o banco de dados
 * @param string $sql Consulta SQL com placeholders (?)
 * @param string $types Tipos de parâmetros (s: string, i: integer, d: double, b: blob)
 * @param array $params Parâmetros para a consulta
 * @return mysqli_stmt|bool Statement preparado ou false em caso de erro
 */
function executePreparedQuery($conn, $sql, $types, $params) {
    $stmt = $conn->prepare($sql);

    if ($stmt === false) {
        error_log("Erro ao preparar consulta: " . $conn->error . " - Query: " . $sql);
        return false;
    }

    if (!empty($params)) {
        // Bind parameters
        $bindParams = array($types);
        foreach ($params as &$param) {
            $bindParams[] = $param;
        }

        call_user_func_array(array($stmt, 'bind_param'), $bindParams);
    }

    if (!$stmt->execute()) {
        error_log("Erro ao executar consulta preparada: " . $stmt->error . " - Query: " . $sql);
        $stmt->close();
        return false;
    }

    return $stmt;
}

/**
 * Função para fechar a conexão com o banco de dados de forma segura
 * @param mysqli $conn Conexão com o banco de dados
 */
function closeDatabase($conn) {
    if ($conn) {
        $conn->close();
    }
}
?>



